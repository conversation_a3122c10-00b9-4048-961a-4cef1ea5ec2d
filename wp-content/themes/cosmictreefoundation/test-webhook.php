<?php

/**
 * Test script for Razorpay webhook integration
 * This script helps test the webhook endpoint without affecting live transactions
 * 
 * Usage: Run this script from command line or browser to test webhook functionality
 * Make sure to update RAZORPAY_WEBHOOK_SECRET in wp-config.php before testing
 */

// Include WordPress
require_once('../../../wp-config.php');
require_once('../../../wp-load.php');

// Test webhook signature verification
function test_webhook_signature_verification()
{
    echo "Testing webhook signature verification...\n";

    // Check if webhook secret is configured
    $webhook_secret = defined('RAZORPAY_WEBHOOK_SECRET') ? RAZORPAY_WEBHOOK_SECRET : '';

    if (!$webhook_secret || $webhook_secret === 'your_webhook_secret_here') {
        echo "⚠ Webhook secret not configured in wp-config.php\n";
        echo "  Please update RAZORPAY_WEBHOOK_SECRET constant\n";
        return false;
    }

    $test_payload = '{"event":"payment.captured","payload":{"payment":{"entity":{"id":"pay_test123","amount":50000,"currency":"INR","status":"captured","order_id":"order_test123","method":"card"}}}}';

    // Generate test signature using the configured secret
    $expected_signature = hash_hmac('sha256', $test_payload, $webhook_secret);

    // Test the verification function
    $result = verify_razorpay_webhook_signature($test_payload, $expected_signature);

    if ($result) {
        echo "✓ Webhook signature verification test PASSED\n";
    } else {
        echo "✗ Webhook signature verification test FAILED\n";
        echo "  Check error logs for details\n";
    }

    return $result;
}

// Test webhook endpoint accessibility
function test_webhook_endpoint_accessibility()
{
    echo "Testing webhook endpoint accessibility...\n";

    $webhook_url = admin_url('admin-ajax.php?action=razorpay_webhook');
    echo "Webhook URL: " . $webhook_url . "\n";

    // Test with a simple GET request (should return method not allowed or similar)
    $response = wp_remote_get($webhook_url);

    if (!is_wp_error($response)) {
        echo "✓ Webhook endpoint is accessible\n";
        return true;
    } else {
        echo "✗ Webhook endpoint is not accessible: " . $response->get_error_message() . "\n";
        return false;
    }
}

// Test order lookup functionality
function test_order_lookup()
{
    echo "Testing order lookup functionality...\n";

    // Create a test order
    $test_order_id = wp_insert_post(array(
        'post_type' => 'order_details',
        'post_status' => 'publish',
        'post_title' => 'Test Webhook Order'
    ));

    if ($test_order_id) {
        // Add test meta data
        update_post_meta($test_order_id, '_razorpay_order_id', 'order_test123');
        update_post_meta($test_order_id, '_order_total_amount', 500); // ₹500
        update_post_meta($test_order_id, '_order_payment_status', 'pending');

        // Test lookup
        $orders = get_posts(array(
            'post_type' => 'order_details',
            'meta_query' => array(
                array(
                    'key' => '_razorpay_order_id',
                    'value' => 'order_test123',
                    'compare' => '='
                )
            ),
            'posts_per_page' => 1,
            'post_status' => 'publish'
        ));

        if (!empty($orders) && $orders[0]->ID == $test_order_id) {
            echo "✓ Order lookup test PASSED\n";

            // Clean up test order
            wp_delete_post($test_order_id, true);
            return true;
        } else {
            echo "✗ Order lookup test FAILED\n";
            wp_delete_post($test_order_id, true);
            return false;
        }
    } else {
        echo "✗ Could not create test order\n";
        return false;
    }
}

// Main test function
function run_webhook_tests()
{
    echo "=== Razorpay Webhook Integration Tests ===\n\n";

    $tests_passed = 0;
    $total_tests = 3;

    // Test 1: Signature verification
    if (test_webhook_signature_verification()) {
        $tests_passed++;
    }
    echo "\n";

    // Test 2: Endpoint accessibility
    if (test_webhook_endpoint_accessibility()) {
        $tests_passed++;
    }
    echo "\n";

    // Test 3: Order lookup
    if (test_order_lookup()) {
        $tests_passed++;
    }
    echo "\n";

    // Summary
    echo "=== Test Results ===\n";
    echo "Tests passed: {$tests_passed}/{$total_tests}\n";

    if ($tests_passed == $total_tests) {
        echo "✓ All tests PASSED! Webhook integration is ready.\n";
        echo "\nNext steps:\n";
        echo "1. Update RAZORPAY_WEBHOOK_SECRET in wp-config.php with your actual webhook secret\n";
        echo "2. Configure webhook URL in Razorpay dashboard: " . admin_url('admin-ajax.php?action=razorpay_webhook') . "\n";
        echo "3. Test with a real payment to ensure everything works\n";
    } else {
        echo "✗ Some tests FAILED. Please check the configuration.\n";
    }
}

// Run tests
if (php_sapi_name() === 'cli') {
    // Command line execution
    run_webhook_tests();
} else {
    // Web execution
    echo "<pre>";
    run_webhook_tests();
    echo "</pre>";
}
