<?php

/**
 * TwentyTen functions and definitions
 *
 * Sets up the theme and provides some helper functions. Some helper functions
 * are used in the theme as custom template tags. Others are attached to action and
 * filter hooks in WordPress to change core functionality.
 *
 * The first function, twentyten_setup(), sets up the theme by registering support
 * for various features in WordPress, such as post thumbnails, navigation menus, and the like.
 *
 * When using a child theme you can override certain functions (those wrapped
 * in a function_exists() call) by defining them first in your child theme's
 * functions.php file. The child theme's functions.php file is included before
 * the parent theme's file, so the child theme functions would be used.
 *
 * @link https://developer.wordpress.org/themes/basics/theme-functions/
 * @link https://developer.wordpress.org/themes/advanced-topics/child-themes/
 *
 * Functions that are not pluggable (not wrapped in function_exists()) are instead attached
 * to a filter or action hook. The hook can be removed by using remove_action() or
 * remove_filter() and you can attach your own function to the hook.
 *
 * We can remove the parent theme's hook only after it is attached, which means we need to
 * wait until setting up the child theme:
 *
 * <code>
 * add_action( 'after_setup_theme', 'my_child_theme_setup' );
 * function my_child_theme_setup() {
 *     // We are providing our own filter for excerpt_length (or using the unfiltered value).
 *     remove_filter( 'excerpt_length', 'twentyten_excerpt_length' );
 *     ...
 * }
 * </code>
 *
 * For more information on hooks, actions, and filters, see https://developer.wordpress.org/plugins/.
 *
 * @package WordPress
 * @subpackage Twenty_Ten
 * @since Twenty Ten 1.0
 */

/*
 * Set the content width based on the theme's design and stylesheet.
 *
 * Used to set the width of images and content. Should be equal to the width the theme
 * is designed for, generally via the style.css stylesheet.
 */
if (! isset($content_width)) {
	$content_width = 640;
}

/* Tell WordPress to run twentyten_setup() when the 'after_setup_theme' hook is run. */
add_action('after_setup_theme', 'twentyten_setup');

if (! function_exists('twentyten_setup')) :
	/**
	 * Set up theme defaults and registers support for various WordPress features.
	 *
	 * Note that this function is hooked into the after_setup_theme hook, which runs
	 * before the init hook. The init hook is too late for some features, such as indicating
	 * support post thumbnails.
	 *
	 * To override twentyten_setup() in a child theme, add your own twentyten_setup to your child theme's
	 * functions.php file.
	 *
	 * @uses add_theme_support()        To add support for post thumbnails, custom headers and backgrounds, and automatic feed links.
	 * @uses register_nav_menus()       To add support for navigation menus.
	 * @uses add_editor_style()         To style the visual editor.
	 * @uses load_theme_textdomain()    For translation/localization support.
	 * @uses register_default_headers() To register the default custom header images provided with the theme.
	 * @uses set_post_thumbnail_size()  To set a custom post thumbnail size.
	 *
	 * @since Twenty Ten 1.0
	 */
	function twentyten_setup()
	{

		// This theme styles the visual editor with editor-style.css to match the theme style.
		add_editor_style();

		// Load regular editor styles into the new block-based editor.
		add_theme_support('editor-styles');

		// Load default block styles.
		add_theme_support('wp-block-styles');

		// Add support for custom color scheme.
		add_theme_support(
			'editor-color-palette',
			array(
				array(
					'name'  => __('Blue', 'twentyten'),
					'slug'  => 'blue',
					'color' => '#0066cc',
				),
				array(
					'name'  => __('Black', 'twentyten'),
					'slug'  => 'black',
					'color' => '#000',
				),
				array(
					'name'  => __('Medium Gray', 'twentyten'),
					'slug'  => 'medium-gray',
					'color' => '#666',
				),
				array(
					'name'  => __('Light Gray', 'twentyten'),
					'slug'  => 'light-gray',
					'color' => '#f1f1f1',
				),
				array(
					'name'  => __('White', 'twentyten'),
					'slug'  => 'white',
					'color' => '#fff',
				),
			)
		);

		// Post Format support. You can also use the legacy "gallery" or "asides" (note the plural) categories.
		add_theme_support('post-formats', array('aside', 'gallery'));

		// This theme uses post thumbnails.
		add_theme_support('post-thumbnails');

		// Add default posts and comments RSS feed links to head.
		add_theme_support('automatic-feed-links');

		/*
		 * Make theme available for translation.
		 * Translations can be filed in the /languages/ directory.
		 */
		load_theme_textdomain('twentyten', get_template_directory() . '/languages');

		// This theme uses wp_nav_menu() in one location.
		register_nav_menus(
			array(
				'primary' => __('Primary Navigation', 'twentyten'),
			)
		);

		// This theme allows users to set a custom background.
		add_theme_support(
			'custom-background',
			array(
				// Let WordPress know what our default background color is.
				'default-color' => 'f1f1f1',
			)
		);

		// The custom header business starts here.

		$custom_header_support = array(
			/*
			 * The default image to use.
			 * The %s is a placeholder for the theme template directory URI.
			 */
			'default-image'       => '%s/images/headers/path.jpg',
			// The height and width of our custom header.
			/**
			 * Filters the Twenty Ten default header image width.
			 *
			 * @since Twenty Ten 1.0
			 *
			 * @param int The default header image width in pixels. Default 940.
			 */
			'width'               => apply_filters('twentyten_header_image_width', 940),
			/**
			 * Filters the Twenty Ten defaul header image height.
			 *
			 * @since Twenty Ten 1.0
			 *
			 * @param int The default header image height in pixels. Default 198.
			 */
			'height'              => apply_filters('twentyten_header_image_height', 198),
			// Support flexible heights.
			'flex-height'         => true,
			// Don't support text inside the header image.
			'header-text'         => false,
			// Callback for styling the header preview in the admin.
			'admin-head-callback' => 'twentyten_admin_header_style',
		);

		add_theme_support('custom-header', $custom_header_support);

		if (! function_exists('get_custom_header')) {
			// This is all for compatibility with versions of WordPress prior to 3.4.
			define('HEADER_TEXTCOLOR', '');
			define('NO_HEADER_TEXT', true);
			define('HEADER_IMAGE', $custom_header_support['default-image']);
			define('HEADER_IMAGE_WIDTH', $custom_header_support['width']);
			define('HEADER_IMAGE_HEIGHT', $custom_header_support['height']);
			add_custom_image_header('', $custom_header_support['admin-head-callback']);
			add_custom_background();
		}

		/*
		 * We'll be using post thumbnails for custom header images on posts and pages.
		 * We want them to be 940 pixels wide by 198 pixels tall.
		 * Larger images will be auto-cropped to fit, smaller ones will be ignored. See header.php.
		 */
		set_post_thumbnail_size($custom_header_support['width'], $custom_header_support['height'], true);

		// ...and thus ends the custom header business.

		// Default custom headers packaged with the theme. %s is a placeholder for the theme template directory URI.
		register_default_headers(
			array(
				'berries'       => array(
					'url'           => '%s/images/headers/berries.jpg',
					'thumbnail_url' => '%s/images/headers/berries-thumbnail.jpg',
					/* translators: Header image description. */
					'description'   => __('Berries', 'twentyten'),
				),
				'cherryblossom' => array(
					'url'           => '%s/images/headers/cherryblossoms.jpg',
					'thumbnail_url' => '%s/images/headers/cherryblossoms-thumbnail.jpg',
					/* translators: Header image description. */
					'description'   => __('Cherry Blossoms', 'twentyten'),
				),
				'concave'       => array(
					'url'           => '%s/images/headers/concave.jpg',
					'thumbnail_url' => '%s/images/headers/concave-thumbnail.jpg',
					/* translators: Header image description. */
					'description'   => __('Concave', 'twentyten'),
				),
				'fern'          => array(
					'url'           => '%s/images/headers/fern.jpg',
					'thumbnail_url' => '%s/images/headers/fern-thumbnail.jpg',
					/* translators: Header image description. */
					'description'   => __('Fern', 'twentyten'),
				),
				'forestfloor'   => array(
					'url'           => '%s/images/headers/forestfloor.jpg',
					'thumbnail_url' => '%s/images/headers/forestfloor-thumbnail.jpg',
					/* translators: Header image description. */
					'description'   => __('Forest Floor', 'twentyten'),
				),
				'inkwell'       => array(
					'url'           => '%s/images/headers/inkwell.jpg',
					'thumbnail_url' => '%s/images/headers/inkwell-thumbnail.jpg',
					/* translators: Header image description. */
					'description'   => __('Inkwell', 'twentyten'),
				),
				'path'          => array(
					'url'           => '%s/images/headers/path.jpg',
					'thumbnail_url' => '%s/images/headers/path-thumbnail.jpg',
					/* translators: Header image description. */
					'description'   => __('Path', 'twentyten'),
				),
				'sunset'        => array(
					'url'           => '%s/images/headers/sunset.jpg',
					'thumbnail_url' => '%s/images/headers/sunset-thumbnail.jpg',
					/* translators: Header image description. */
					'description'   => __('Sunset', 'twentyten'),
				),
			)
		);
	}
endif;

if (! function_exists('twentyten_admin_header_style')) :
	/**
	 * Style the header image displayed on the Appearance > Header admin panel.
	 *
	 * Referenced via add_custom_image_header() in twentyten_setup().
	 *
	 * @since Twenty Ten 1.0
	 */
	function twentyten_admin_header_style()
	{
?>
		<style type="text/css" id="twentyten-admin-header-css">
			/* Shows the same border as on front end */
			#headimg {
				border-bottom: 1px solid #000;
				border-top: 4px solid #000;
			}

			/* If header-text was supported, you would style the text with these selectors:
	#headimg #name { }
	#headimg #desc { }
	*/
		</style>
		<?php
	}
endif;

/**
 * Show a home link for our wp_nav_menu() fallback, wp_page_menu().
 *
 * To override this in a child theme, remove the filter and optionally add
 * your own function tied to the wp_page_menu_args filter hook.
 *
 * @since Twenty Ten 1.0
 *
 * @param array $args An optional array of arguments. @see wp_page_menu()
 */
function twentyten_page_menu_args($args)
{
	if (! isset($args['show_home'])) {
		$args['show_home'] = true;
	}
	return $args;
}
add_filter('wp_page_menu_args', 'twentyten_page_menu_args');

/**
 * Set the post excerpt length to 40 characters.
 *
 * To override this length in a child theme, remove the filter and add your own
 * function tied to the excerpt_length filter hook.
 *
 * @since Twenty Ten 1.0
 *
 * @param int $length The number of excerpt characters.
 * @return int The filtered number of excerpt characters.
 */
function twentyten_excerpt_length($length)
{
	return 40;
}
add_filter('excerpt_length', 'twentyten_excerpt_length');

if (! function_exists('twentyten_continue_reading_link')) :
	/**
	 * Return a "Continue Reading" link for excerpts.
	 *
	 * @since Twenty Ten 1.0
	 *
	 * @return string "Continue reading <span class="meta-nav">&rarr;</span>" link.
	 */
	function twentyten_continue_reading_link()
	{
		return ' <a href="' . esc_url(get_permalink()) . '">' . __('Continue reading <span class="meta-nav">&rarr;</span>', 'twentyten') . '</a>';
	}
endif;

/**
 * Replace "[...]" with an ellipsis and twentyten_continue_reading_link().
 *
 * "[...]" is appended to automatically generated excerpts.
 *
 * To override this in a child theme, remove the filter and add your own
 * function tied to the excerpt_more filter hook.
 *
 * @since Twenty Ten 1.0
 *
 * @param string $more The Read More text.
 * @return string The filtered Read More text.
 */
function twentyten_auto_excerpt_more($more)
{
	if (! is_admin()) {
		return ' &hellip;' . twentyten_continue_reading_link();
	}
	return $more;
}
add_filter('excerpt_more', 'twentyten_auto_excerpt_more');

/**
 * Add a pretty "Continue Reading" link to custom post excerpts.
 *
 * To override this link in a child theme, remove the filter and add your own
 * function tied to the get_the_excerpt filter hook.
 *
 * @since Twenty Ten 1.0
 *
 * @param string $output The "Continue Reading" link.
 * @return string Excerpt with a pretty "Continue Reading" link.
 */
function twentyten_custom_excerpt_more($output)
{
	if (has_excerpt() && ! is_attachment() && ! is_admin()) {
		$output .= twentyten_continue_reading_link();
	}
	return $output;
}
add_filter('get_the_excerpt', 'twentyten_custom_excerpt_more');

/**
 * Remove inline styles printed when the gallery shortcode is used.
 *
 * Galleries are styled by the theme in Twenty Ten's style.css. This is just
 * a simple filter call that tells WordPress to not use the default styles.
 *
 * @since Twenty Ten 1.2
 */
add_filter('use_default_gallery_style', '__return_false');

/**
 * Deprecated way to remove inline styles printed when the gallery shortcode is used.
 *
 * This function is no longer needed or used. Use the use_default_gallery_style
 * filter instead, as seen above.
 *
 * @since Twenty Ten 1.0
 * @deprecated Deprecated in Twenty Ten 1.2 for WordPress 3.1
 *
 * @return string The gallery style filter, with the styles themselves removed.
 */
function twentyten_remove_gallery_css($css)
{
	return preg_replace("#<style type='text/css'>(.*?)</style>#s", '', $css);
}
// Backward compatibility with WordPress 3.0.
if (version_compare($GLOBALS['wp_version'], '3.1', '<')) {
	add_filter('gallery_style', 'twentyten_remove_gallery_css');
}

if (! function_exists('twentyten_comment')) :
	/**
	 * Template for comments and pingbacks.
	 *
	 * To override this walker in a child theme without modifying the comments template
	 * simply create your own twentyten_comment(), and that function will be used instead.
	 *
	 * Used as a callback by wp_list_comments() for displaying the comments.
	 *
	 * @since Twenty Ten 1.0
	 *
	 * @param WP_Comment $comment The comment object.
	 * @param array      $args    An array of arguments. @see get_comment_reply_link()
	 * @param int        $depth   The depth of the comment.
	 */
	function twentyten_comment($comment, $args, $depth)
	{
		$GLOBALS['comment'] = $comment;
		switch ($comment->comment_type):
			case '':
			case 'comment':
		?>
				<li <?php comment_class(); ?> id="li-comment-<?php comment_ID(); ?>">
					<div id="comment-<?php comment_ID(); ?>">
						<div class="comment-author vcard">
							<?php echo get_avatar($comment, 40); ?>
							<?php
							/* translators: %s: Author display name. */
							printf(__('%s <span class="says">says:</span>', 'twentyten'), sprintf('<cite class="fn">%s</cite>', get_comment_author_link()));
							?>
						</div><!-- .comment-author .vcard -->

						<?php
						$commenter = wp_get_current_commenter();
						if ($commenter['comment_author_email']) {
							$moderation_note = __('Your comment is awaiting moderation.', 'twentyten');
						} else {
							$moderation_note = __('Your comment is awaiting moderation. This is a preview; your comment will be visible after it has been approved.', 'twentyten');
						}
						?>

						<?php if ('0' == $comment->comment_approved) : ?>
							<em class="comment-awaiting-moderation"><?php echo $moderation_note; ?></em>
							<br />
						<?php endif; ?>

						<div class="comment-meta commentmetadata"><a href="<?php echo esc_url(get_comment_link($comment->comment_ID)); ?>">
								<?php
								/* translators: 1: Date, 2: Time. */
								printf(__('%1$s at %2$s', 'twentyten'), get_comment_date(), get_comment_time());
								?>
							</a>
							<?php
							edit_comment_link(__('(Edit)', 'twentyten'), ' ');
							?>
						</div><!-- .comment-meta .commentmetadata -->

						<div class="comment-body"><?php comment_text(); ?></div>

						<div class="reply">
							<?php
							comment_reply_link(
								array_merge(
									$args,
									array(
										'depth'     => $depth,
										'max_depth' => $args['max_depth'],
									)
								)
							);
							?>
						</div><!-- .reply -->
					</div><!-- #comment-##  -->

				<?php
				break;
			case 'pingback':
			case 'trackback':
				?>
				<li class="post pingback">
					<p><?php _e('Pingback:', 'twentyten'); ?> <?php comment_author_link(); ?><?php edit_comment_link(__('(Edit)', 'twentyten'), ' '); ?></p>
		<?php
				break;
		endswitch;
	}
endif;

/**
 * Register widgetized areas, including two sidebars and four widget-ready columns in the footer.
 *
 * To override twentyten_widgets_init() in a child theme, remove the action hook and add your own
 * function tied to the init hook.
 *
 * @since Twenty Ten 1.0
 *
 * @uses register_sidebar()
 */
function twentyten_widgets_init()
{
	// Area 1, located at the top of the sidebar.
	register_sidebar(
		array(
			'name'          => __('Primary Widget Area', 'twentyten'),
			'id'            => 'primary-widget-area',
			'description'   => __('Add widgets here to appear in your sidebar.', 'twentyten'),
			'before_widget' => '<li id="%1$s" class="widget-container %2$s">',
			'after_widget'  => '</li>',
			'before_title'  => '<h3 class="widget-title">',
			'after_title'   => '</h3>',
		)
	);

	// Area 2, located below the Primary Widget Area in the sidebar. Empty by default.
	register_sidebar(
		array(
			'name'          => __('Secondary Widget Area', 'twentyten'),
			'id'            => 'secondary-widget-area',
			'description'   => __('An optional secondary widget area, displays below the primary widget area in your sidebar.', 'twentyten'),
			'before_widget' => '<li id="%1$s" class="widget-container %2$s">',
			'after_widget'  => '</li>',
			'before_title'  => '<h3 class="widget-title">',
			'after_title'   => '</h3>',
		)
	);

	// Area 3, located in the footer. Empty by default.
	register_sidebar(
		array(
			'name'          => __('First Footer Widget Area', 'twentyten'),
			'id'            => 'first-footer-widget-area',
			'description'   => __('An optional widget area for your site footer.', 'twentyten'),
			'before_widget' => '<li id="%1$s" class="widget-container %2$s">',
			'after_widget'  => '</li>',
			'before_title'  => '<h3 class="widget-title">',
			'after_title'   => '</h3>',
		)
	);

	// Area 4, located in the footer. Empty by default.
	register_sidebar(
		array(
			'name'          => __('Second Footer Widget Area', 'twentyten'),
			'id'            => 'second-footer-widget-area',
			'description'   => __('An optional widget area for your site footer.', 'twentyten'),
			'before_widget' => '<li id="%1$s" class="widget-container %2$s">',
			'after_widget'  => '</li>',
			'before_title'  => '<h3 class="widget-title">',
			'after_title'   => '</h3>',
		)
	);

	// Area 5, located in the footer. Empty by default.
	register_sidebar(
		array(
			'name'          => __('Third Footer Widget Area', 'twentyten'),
			'id'            => 'third-footer-widget-area',
			'description'   => __('An optional widget area for your site footer.', 'twentyten'),
			'before_widget' => '<li id="%1$s" class="widget-container %2$s">',
			'after_widget'  => '</li>',
			'before_title'  => '<h3 class="widget-title">',
			'after_title'   => '</h3>',
		)
	);

	// Area 6, located in the footer. Empty by default.
	register_sidebar(
		array(
			'name'          => __('Fourth Footer Widget Area', 'twentyten'),
			'id'            => 'fourth-footer-widget-area',
			'description'   => __('An optional widget area for your site footer.', 'twentyten'),
			'before_widget' => '<li id="%1$s" class="widget-container %2$s">',
			'after_widget'  => '</li>',
			'before_title'  => '<h3 class="widget-title">',
			'after_title'   => '</h3>',
		)
	);
}
/** Register sidebars by running twentyten_widgets_init() on the widgets_init hook. */
add_action('widgets_init', 'twentyten_widgets_init');

/**
 * Remove the default styles that are packaged with the Recent Comments widget.
 *
 * To override this in a child theme, remove the filter and optionally add your own
 * function tied to the widgets_init action hook.
 *
 * This function uses a filter (show_recent_comments_widget_style) new in WordPress 3.1
 * to remove the default style. Using Twenty Ten 1.2 in WordPress 3.0 will show the styles,
 * but they won't have any effect on the widget in default Twenty Ten styling.
 *
 * @since Twenty Ten 1.0
 */
function twentyten_remove_recent_comments_style()
{
	add_filter('show_recent_comments_widget_style', '__return_false');
}
add_action('widgets_init', 'twentyten_remove_recent_comments_style');

if (! function_exists('twentyten_posted_on')) :
	/**
	 * Print HTML with meta information for the current post-date/time and author.
	 *
	 * @since Twenty Ten 1.0
	 */
	function twentyten_posted_on()
	{
		printf(
			/* translators: 1: CSS classes, 2: Date, 3: Author display name. */
			__('<span class="%1$s">Posted on</span> %2$s <span class="meta-sep">by</span> %3$s', 'twentyten'),
			'meta-prep meta-prep-author',
			sprintf(
				'<a href="%1$s" title="%2$s" rel="bookmark"><span class="entry-date">%3$s</span></a>',
				esc_url(get_permalink()),
				esc_attr(get_the_time()),
				get_the_date()
			),
			sprintf(
				'<span class="author vcard"><a class="url fn n" href="%1$s" title="%2$s">%3$s</a></span>',
				esc_url(get_author_posts_url(get_the_author_meta('ID'))),
				/* translators: %s: Author display name. */
				esc_attr(sprintf(__('View all posts by %s', 'twentyten'), get_the_author())),
				get_the_author()
			)
		);
	}
endif;

if (! function_exists('twentyten_posted_in')) :
	/**
	 * Print HTML with meta information for the current post (category, tags and permalink).
	 *
	 * @since Twenty Ten 1.0
	 */
	function twentyten_posted_in()
	{
		// Retrieves tag list of current post, separated by commas.
		$tags_list = get_the_tag_list('', ', ');

		if ($tags_list && ! is_wp_error($tags_list)) {
			/* translators: 1: Category name, 2: Tag name, 3: Post permalink, 4: Post title. */
			$posted_in = __('This entry was posted in %1$s and tagged %2$s. Bookmark the <a href="%3$s" title="Permalink to %4$s" rel="bookmark">permalink</a>.', 'twentyten');
		} elseif (is_object_in_taxonomy(get_post_type(), 'category')) {
			/* translators: 1: Category name, 3: Post permalink, 4: Post title. */
			$posted_in = __('This entry was posted in %1$s. Bookmark the <a href="%3$s" title="Permalink to %4$s" rel="bookmark">permalink</a>.', 'twentyten');
		} else {
			/* translators: 3: Post permalink, 4: Post title. */
			$posted_in = __('Bookmark the <a href="%3$s" title="Permalink to %4$s" rel="bookmark">permalink</a>.', 'twentyten');
		}

		// Prints the string, replacing the placeholders.
		printf(
			$posted_in,
			get_the_category_list(', '),
			$tags_list,
			esc_url(get_permalink()),
			the_title_attribute('echo=0')
		);
	}
endif;

/**
 * Retrieve the IDs for images in a gallery.
 *
 * @uses get_post_galleries() First, if available. Falls back to shortcode parsing,
 *                            then as last option uses a get_posts() call.
 *
 * @since Twenty Ten 1.6.
 *
 * @return array List of image IDs from the post gallery.
 */
function twentyten_get_gallery_images()
{
	$images = array();

	if (function_exists('get_post_galleries')) {
		$galleries = get_post_galleries(get_the_ID(), false);
		if (isset($galleries[0]['ids'])) {
			$images = explode(',', $galleries[0]['ids']);
		}
	} else {
		$pattern = get_shortcode_regex();
		preg_match("/$pattern/s", get_the_content(), $match);
		$atts = shortcode_parse_atts($match[3]);
		if (isset($atts['ids'])) {
			$images = explode(',', $atts['ids']);
		}
	}

	if (! $images) {
		$images = get_posts(
			array(
				'fields'         => 'ids',
				'numberposts'    => 999,
				'order'          => 'ASC',
				'orderby'        => 'menu_order',
				'post_mime_type' => 'image',
				'post_parent'    => get_the_ID(),
				'post_type'      => 'attachment',
			)
		);
	}

	return $images;
}

/**
 * Modifies tag cloud widget arguments to display all tags in the same font size
 * and use list format for better accessibility.
 *
 * @since Twenty Ten 2.4
 *
 * @param array $args Arguments for tag cloud widget.
 * @return array The filtered arguments for tag cloud widget.
 */
function twentyten_widget_tag_cloud_args($args)
{
	$args['largest']  = 22;
	$args['smallest'] = 8;
	$args['unit']     = 'pt';
	$args['format']   = 'list';

	return $args;
}
add_filter('widget_tag_cloud_args', 'twentyten_widget_tag_cloud_args');

/**
 * Enqueue scripts and styles for front end.
 *
 * @since Twenty Ten 2.6
 */
function twentyten_scripts_styles()
{
	// Theme block stylesheet.
	wp_enqueue_style('twentyten-block-style', get_template_directory_uri() . '/blocks.css', array(), '20190704');
}
add_action('wp_enqueue_scripts', 'twentyten_scripts_styles');

/**
 * Enqueue styles for the block-based editor.
 *
 * @since Twenty Ten 2.6
 */
function twentyten_block_editor_styles()
{
	// Block styles.
	wp_enqueue_style('twentyten-block-editor-style', get_template_directory_uri() . '/editor-blocks.css', array(), '20221011');
}
add_action('enqueue_block_editor_assets', 'twentyten_block_editor_styles');

// Block Patterns.
require get_template_directory() . '/block-patterns.php';

// Include Order PDF Generator
require get_template_directory() . '/includes/order-pdf-generator.php';

if (! function_exists('wp_body_open')) :
	/**
	 * Fire the wp_body_open action.
	 *
	 * Added for backward compatibility to support pre-5.2.0 WordPress versions.
	 *
	 * @since Twenty Ten 2.9
	 */
	function wp_body_open()
	{
		/**
		 * Triggered after the opening <body> tag.
		 *
		 * @since Twenty Ten 2.9
		 */
		do_action('wp_body_open');
	}
endif;

add_action('init', 'create_post_type_hslider');
function create_post_type_hslider()
{
	register_post_type(
		'hslider_post',
		array(
			'labels' => array(
				'name' => __('Slider'),
				'all_items' => __('Slider'),
				'singular_name' => __('Slider'),
				'rewrite' => array('slug' => 'hslider_post')
			),
			'public' => true,
			'has_archive' => true,
			'supports' => array('title', 'editor', 'thumbnail', "page-attributes"),
			// 'menu_position' => 5,
			//'menu_icon' => get_template_directory_uri().'/img/team.png',
		)
	);
}

add_action('init', 'create_post_type_htread');
function create_post_type_htread()
{
	register_post_type(
		'htread_post',
		array(
			'labels' => array(
				'name' => __('Tarot Reading'),
				'all_items' => __('Tarot Reading'),
				'singular_name' => __('Tarot Reading'),
				'rewrite' => array('slug' => 'htread_post')
			),
			'public' => true,
			'has_archive' => true,
			'supports' => array('title', 'editor', 'thumbnail', "page-attributes"),
			// 'menu_position' => 5,
			//'menu_icon' => get_template_directory_uri().'/img/team.png',
		)
	);
}

add_action('init', 'create_post_type_hestimonials');
function create_post_type_hestimonials()
{
	register_post_type(
		'hestimonials_post',
		array(
			'labels' => array(
				'name' => __('Testimonials'),
				'all_items' => __('Testimonials'),
				'singular_name' => __('Testimonials'),
				'rewrite' => array('slug' => 'hestimonials_post')
			),
			'public' => true,
			'has_archive' => true,
			'supports' => array('title', 'editor', 'thumbnail', "page-attributes"),
			// 'menu_position' => 5,
			//'menu_icon' => get_template_directory_uri().'/img/team.png',
		)
	);
}

add_action('init', 'create_post_type_fcarousel');
function create_post_type_fcarousel()
{
	register_post_type(
		'fcarousel_post',
		array(
			'labels' => array(
				'name' => __('Foot Carousel'),
				'all_items' => __('Foot Carousel'),
				'singular_name' => __('Foot Carousel'),
				'rewrite' => array('slug' => 'fcarousel_post')
			),
			'public' => true,
			'has_archive' => true,
			'supports' => array('title', 'editor', 'thumbnail', "page-attributes"),
			// 'menu_position' => 5,
			//'menu_icon' => get_template_directory_uri().'/img/team.png',
		)
	);
}

add_action('init', 'create_post_type_hvdotestis');
function create_post_type_hvdotestis()
{
	register_post_type(
		'hvdotestis_post',
		array(
			'labels' => array(
				'name' => __('Video Testimonials'),
				'all_items' => __('Video Testimonials'),
				'singular_name' => __('Video Testimonials'),
				'rewrite' => array('slug' => 'hvdotestis_post')
			),
			'public' => true,
			'has_archive' => true,
			'supports' => array('title', 'editor', 'thumbnail', "page-attributes"),
			// 'menu_position' => 5,
			//'menu_icon' => get_template_directory_uri().'/img/team.png',
		)
	);
}

add_action('init', 'create_post_type_hservices');
function create_post_type_hservices()
{
	register_post_type(
		'hservices_post',
		array(
			'labels' => array(
				'name' => __('Services'),
				'all_items' => __('Services'),
				'singular_name' => __('Services'),
				'rewrite' => array('slug' => 'hservices_post')
			),
			'public' => true,
			'has_archive' => true,
			'supports' => array('title', 'editor', 'thumbnail', "page-attributes"),
			// 'menu_position' => 5,
			//'menu_icon' => get_template_directory_uri().'/img/team.png',
		)
	);
}


add_action('init', 'create_posttype_upevents');
function create_posttype_upevents()
{
	register_post_type(
		'upevents',
		array(
			'labels' => array(
				'name' => __('Upcoming Events'),
				'singular_name' => __('Upcoming Events')
			),
			'public' => true,
			'has_archive' => true,
			'taxonomies' => array('category'),
			'rewrite' => array('slug' => 'upevents'),
			'supports' => apply_filters('popmake_popup_supports', array('title', 'editor', 'author', 'thumbnail')),

		)
	);
}

add_action('init', 'create_posttype_hworkshop');
function create_posttype_hworkshop()
{
	register_post_type(
		'hworkshop',
		array(
			'labels' => array(
				'name' => __('Workshops'),
				'singular_name' => __('Workshops')
			),
			'public' => true,
			'has_archive' => true,
			'taxonomies' => array('category'),
			'rewrite' => array('slug' => 'hworkshop'),
			'supports' => apply_filters('popmake_popup_supports', array('title', 'editor', 'author', 'thumbnail')),

		)
	);
}


/********************************** Event Booking Start *********************************/
// Define REQUESTS_SILENCE_PSR0_DEPRECATIONS to suppress PSR-0 deprecation warnings
if (!defined('REQUESTS_SILENCE_PSR0_DEPRECATIONS')) {
	define('REQUESTS_SILENCE_PSR0_DEPRECATIONS', true);
}

// Include Composer autoloader for endroid/qr-code
require_once get_template_directory() . '/vendor/autoload.php';

// Include Razorpay PHP SDK
require_once get_template_directory() . '/vendor/razorpay/razorpay/Razorpay.php';

use Endroid\QrCode\QrCode;
use Endroid\QrCode\Writer\PngWriter;

/************ Register Custom Post Type for Events ************/
function create_events_post_type()
{
	$labels = array(
		'name'                  => 'Events',
		'singular_name'         => 'Event',
		'menu_name'             => 'Events',
		'name_admin_bar'        => 'Event',
		'add_new'               => 'Add New',
		'add_new_item'          => 'Add New Event',
		'new_item'              => 'New Event',
		'edit_item'             => 'Edit Event',
		'view_item'             => 'View Event',
		'all_items'             => 'All Events',
		'search_items'          => 'Search Events',
		'not_found'             => 'No events found.',
		'not_found_in_trash'    => 'No events found in Trash.',
	);

	$args = array(
		'labels'             => $labels,
		'public'             => true,
		'show_ui'            => true,
		'show_in_menu'       => true,
		'menu_icon'          => 'dashicons-calendar-alt',
		'supports'           => array('title', 'editor', 'thumbnail'),
		'has_archive'        => true,
		'rewrite'            => array('slug' => 'events'),
		'show_in_rest'       => true,
		'taxonomies'         => array('category'),
		'menu_position'      => 6,
	);

	register_post_type('events', $args);
	error_log('Events post type registered');
}
add_action('init', 'create_events_post_type');

function events_add_meta_boxes()
{
	add_meta_box(
		'event_details',
		'Event Details',
		'event_meta_box_callback',
		'events',
		'normal',
		'default'
	);
}
add_action('add_meta_boxes', 'events_add_meta_boxes');

function event_meta_box_callback($post)
{
	wp_nonce_field('event_meta_nonce', 'event_meta_nonce');

	// Check for validation error messages
	$error_message = get_transient('event_meta_error_' . $post->ID);
	if ($error_message) {
		// Display the error message
		echo '<div class="notice notice-warning inline"><p>' . esc_html($error_message) . '</p></div>';
		// Delete the transient so it doesn't show again
		delete_transient('event_meta_error_' . $post->ID);
	}

	$venue                = get_post_meta($post->ID, '_event_venue', true);
	$date                 = get_post_meta($post->ID, '_event_date', true);
	$total                = get_post_meta($post->ID, '_event_total_tickets', true);
	$sold                 = get_post_meta($post->ID, '_event_sold_tickets', true);
	$available            = (is_numeric($total) && is_numeric($sold)) ? $total - $sold : 0;

	// Gold and Violet ticket quantities
	$total_gold_tickets   = get_post_meta($post->ID, '_event_total_gold_tickets', true);
	$total_violet_tickets = get_post_meta($post->ID, '_event_total_violet_tickets', true);
	$sold_gold_tickets    = get_post_meta($post->ID, '_event_sold_gold_tickets', true);
	$sold_violet_tickets  = get_post_meta($post->ID, '_event_sold_violet_tickets', true);

	// Calculate available tickets by type
	$available_gold       = (is_numeric($total_gold_tickets) && is_numeric($sold_gold_tickets)) ? $total_gold_tickets - $sold_gold_tickets : 0;
	$available_violet     = (is_numeric($total_violet_tickets) && is_numeric($sold_violet_tickets)) ? $total_violet_tickets - $sold_violet_tickets : 0;

	// Pass variation fields
	$gold_pass_price      = get_post_meta($post->ID, '_event_gold_pass_price', true);
	$violet_pass_price    = get_post_meta($post->ID, '_event_violet_pass_price', true);
	$gold_pass_early_bird = get_post_meta($post->ID, '_event_gold_pass_early_bird', true);
	$violet_pass_early_bird = get_post_meta($post->ID, '_event_violet_pass_early_bird', true);
	$early_bird_cutoff    = get_post_meta($post->ID, '_event_early_bird_cutoff', true);

	// Set default early bird cutoff date if not set
	if (empty($early_bird_cutoff)) {
		$early_bird_cutoff = defined('EARLY_BIRD_CUTOFF_DATE') ? EARLY_BIRD_CUTOFF_DATE : '2025-07-15';
	}

	// Set default prices if not set
	if (empty($gold_pass_price)) $gold_pass_price = 12000;
	if (empty($violet_pass_price)) $violet_pass_price = 8800;
	if (empty($gold_pass_early_bird)) $gold_pass_early_bird = 11110;
	if (empty($violet_pass_early_bird)) $violet_pass_early_bird = 7770;
		?>
		<div>
			<label for="event_venue">Venue</label>
			<input type="text" id="event_venue" name="event_venue" value="<?php echo esc_attr($venue); ?>" class="widefat" />
		</div>
		<div>
			<label for="event_date">Event Date</label>
			<input type="date" id="event_date" name="event_date" value="<?php echo esc_attr($date); ?>" class="widefat" />
		</div>
		<div>
			<label for="event_total_tickets">Total Tickets (All Types)</label>
			<input type="number" id="event_total_tickets" name="event_total_tickets" value="<?php echo esc_attr($total); ?>" min="0" class="widefat" />
		</div>
		<div>
			<label for="event_sold_tickets">Sold Tickets (All Types)</label>
			<input type="number" id="event_sold_tickets" name="event_sold_tickets" value="<?php echo esc_attr($sold); ?>" min="0" readonly class="widefat" />
		</div>
		<div>
			<label for="event_available_tickets">Available Tickets (All Types)</label>
			<input type="number" id="event_available_tickets" value="<?php echo esc_attr($available); ?>" readonly class="widefat" />
		</div>

		<h3 style="margin-top: 20px; padding-top: 10px; border-top: 1px solid #ddd;">Ticket Type Management</h3>

		<div style="display: flex; gap: 20px; margin-top: 15px;">
			<div style="flex: 1;">
				<h4>Gold Tickets</h4>
				<div>
					<label for="event_total_gold_tickets">Total Gold Tickets</label>
					<input type="number" id="event_total_gold_tickets" name="event_total_gold_tickets" value="<?php echo esc_attr($total_gold_tickets); ?>" min="0" class="widefat" />
				</div>
				<div>
					<label for="event_sold_gold_tickets">Sold Gold Tickets</label>
					<input type="number" id="event_sold_gold_tickets" name="event_sold_gold_tickets" value="<?php echo esc_attr($sold_gold_tickets); ?>" min="0" readonly class="widefat" />
				</div>
				<div>
					<label for="event_available_gold_tickets">Available Gold Tickets</label>
					<input type="number" id="event_available_gold_tickets" value="<?php echo esc_attr($available_gold); ?>" readonly class="widefat" />
				</div>
			</div>

			<div style="flex: 1;">
				<h4>Violet Tickets</h4>
				<div>
					<label for="event_total_violet_tickets">Total Violet Tickets</label>
					<input type="number" id="event_total_violet_tickets" name="event_total_violet_tickets" value="<?php echo esc_attr($total_violet_tickets); ?>" min="0" class="widefat" />
				</div>
				<div>
					<label for="event_sold_violet_tickets">Sold Violet Tickets</label>
					<input type="number" id="event_sold_violet_tickets" name="event_sold_violet_tickets" value="<?php echo esc_attr($sold_violet_tickets); ?>" min="0" readonly class="widefat" />
				</div>
				<div>
					<label for="event_available_violet_tickets">Available Violet Tickets</label>
					<input type="number" id="event_available_violet_tickets" value="<?php echo esc_attr($available_violet); ?>" readonly class="widefat" />
				</div>
			</div>
		</div>

		<h3 style="margin-top: 20px; padding-top: 20px; border-top: 1px solid #ddd;">Pass Variations</h3>

		<div>
			<label for="event_early_bird_cutoff">Early Bird Cutoff Date</label>
			<input type="date" id="event_early_bird_cutoff" name="event_early_bird_cutoff" value="<?php echo esc_attr($early_bird_cutoff); ?>" class="widefat" />
			<p class="description">Early bird pricing will be available until this date.</p>
		</div>

		<div style="display: flex; gap: 20px; margin-top: 15px;">
			<div style="flex: 1;">
				<h4>Gold Pass</h4>
				<div>
					<label for="event_gold_pass_price">Regular Price (INR)</label>
					<input type="number" id="event_gold_pass_price" name="event_gold_pass_price" step="0.01" value="<?php echo esc_attr($gold_pass_price); ?>" min="0" class="widefat" />
				</div>
				<div>
					<label for="event_gold_pass_early_bird">Early Bird Price (INR)</label>
					<input type="number" id="event_gold_pass_early_bird" name="event_gold_pass_early_bird" step="0.01" value="<?php echo esc_attr($gold_pass_early_bird); ?>" min="0" class="widefat" />
				</div>
			</div>

			<div style="flex: 1;">
				<h4>Violet Pass</h4>
				<div>
					<label for="event_violet_pass_price">Regular Price (INR)</label>
					<input type="number" id="event_violet_pass_price" name="event_violet_pass_price" step="0.01" value="<?php echo esc_attr($violet_pass_price); ?>" min="0" class="widefat" />
				</div>
				<div>
					<label for="event_violet_pass_early_bird">Early Bird Price (INR)</label>
					<input type="number" id="event_violet_pass_early_bird" name="event_violet_pass_early_bird" step="0.01" value="<?php echo esc_attr($violet_pass_early_bird); ?>" min="0" class="widefat" />
				</div>
			</div>
		</div>
	<?php
}

function save_event_meta_data($post_id)
{
	if (!isset($_POST['event_meta_nonce']) || !wp_verify_nonce($_POST['event_meta_nonce'], 'event_meta_nonce')) {
		error_log('Event meta nonce verification failed for post_id: ' . $post_id);
		return;
	}

	if (get_post_type($post_id) !== 'events') {
		return;
	}

	if (defined('DOING_AUTOSAVE') && DOING_AUTOSAVE) {
		return;
	}

	$fields = array(
		'event_venue'               => 'sanitize_text_field',
		'event_date'                => 'sanitize_text_field',
		'event_total_tickets'       => 'absint',
		'event_sold_tickets'        => 'absint',
		'event_total_gold_tickets'  => 'absint',
		'event_total_violet_tickets' => 'absint',
		'event_sold_gold_tickets'   => 'absint',
		'event_sold_violet_tickets' => 'absint',
		'event_early_bird_cutoff'   => 'sanitize_text_field',
		'event_gold_pass_price'     => 'floatval',
		'event_violet_pass_price'   => 'floatval',
		'event_gold_pass_early_bird' => 'floatval',
		'event_violet_pass_early_bird' => 'floatval',
	);

	// Get the ticket values
	$total = isset($_POST['event_total_tickets']) ? absint($_POST['event_total_tickets']) : 0;
	$total_gold = isset($_POST['event_total_gold_tickets']) ? absint($_POST['event_total_gold_tickets']) : 0;
	$total_violet = isset($_POST['event_total_violet_tickets']) ? absint($_POST['event_total_violet_tickets']) : 0;

	// Check which field was likely modified by the user
	$old_total_gold = absint(get_post_meta($post_id, '_event_total_gold_tickets', true));
	$old_total_violet = absint(get_post_meta($post_id, '_event_total_violet_tickets', true));
	$old_total = absint(get_post_meta($post_id, '_event_total_tickets', true));

	// Validate that gold + violet tickets equal total tickets
	if ($total_gold + $total_violet != $total) {
		// If gold tickets changed, adjust violet tickets
		if ($total_gold != $old_total_gold && $total != $old_total) {
			// Both total and gold changed, adjust violet
			$total_violet = $total - $total_gold;
			if ($total_violet < 0) $total_violet = 0;
			$_POST['event_total_violet_tickets'] = $total_violet;
			set_transient('event_meta_error_' . $post_id, 'Violet tickets have been automatically adjusted to ' . $total_violet . ' to match the total of ' . $total . ' tickets.', 60);
		} else if ($total_gold != $old_total_gold) {
			// Only gold changed, adjust violet
			$total_violet = $total - $total_gold;
			if ($total_violet < 0) $total_violet = 0;
			$_POST['event_total_violet_tickets'] = $total_violet;
			set_transient('event_meta_error_' . $post_id, 'Violet tickets have been automatically adjusted to ' . $total_violet . ' to match the total of ' . $total . ' tickets.', 60);
		}
		// If violet tickets changed, adjust gold tickets
		else if ($total_violet != $old_total_violet && $total != $old_total) {
			// Both total and violet changed, adjust gold
			$total_gold = $total - $total_violet;
			if ($total_gold < 0) $total_gold = 0;
			$_POST['event_total_gold_tickets'] = $total_gold;
			set_transient('event_meta_error_' . $post_id, 'Gold tickets have been automatically adjusted to ' . $total_gold . ' to match the total of ' . $total . ' tickets.', 60);
		} else if ($total_violet != $old_total_violet) {
			// Only violet changed, adjust gold
			$total_gold = $total - $total_violet;
			if ($total_gold < 0) $total_gold = 0;
			$_POST['event_total_gold_tickets'] = $total_gold;
			set_transient('event_meta_error_' . $post_id, 'Gold tickets have been automatically adjusted to ' . $total_gold . ' to match the total of ' . $total . ' tickets.', 60);
		}
		// If only total changed, distribute proportionally
		else if ($total != $old_total) {
			// Calculate the ratio of gold to violet in the old values
			$old_sum = $old_total_gold + $old_total_violet;
			if ($old_sum > 0) {
				$gold_ratio = $old_total_gold / $old_sum;
				$total_gold = round($total * $gold_ratio);
				$total_violet = $total - $total_gold;
			} else {
				// If old sum was 0, distribute evenly
				$total_gold = floor($total / 2);
				$total_violet = $total - $total_gold;
			}

			$_POST['event_total_gold_tickets'] = $total_gold;
			$_POST['event_total_violet_tickets'] = $total_violet;
			set_transient('event_meta_error_' . $post_id, 'Gold and Violet tickets have been automatically adjusted to match the new total of ' . $total . ' tickets.', 60);
		}
		// If none of the above, distribute evenly
		else {
			if ($total > 0) {
				$total_gold = floor($total / 2);
				$total_violet = $total - $total_gold;

				$_POST['event_total_gold_tickets'] = $total_gold;
				$_POST['event_total_violet_tickets'] = $total_violet;
				set_transient('event_meta_error_' . $post_id, 'Gold and Violet tickets have been automatically adjusted to match the total of ' . $total . ' tickets.', 60);
			} else {
				// If total is zero, set gold and violet to zero
				$total_gold = 0;
				$total_violet = 0;
				$_POST['event_total_gold_tickets'] = 0;
				$_POST['event_total_violet_tickets'] = 0;
				set_transient('event_meta_error_' . $post_id, 'All ticket counts have been set to zero.', 60);
			}
		}
	}

	foreach ($fields as $field => $sanitize) {
		if (isset($_POST[$field])) {
			update_post_meta($post_id, '_' . $field, $sanitize($_POST[$field]));
		}
	}

	// Calculate total available tickets
	$sold = isset($_POST['event_sold_tickets']) ? absint($_POST['event_sold_tickets']) : 0;
	$available_total = $total - $sold;
	update_post_meta($post_id, '_event_available_tickets', $available_total);

	// Calculate available Gold tickets
	$sold_gold = isset($_POST['event_sold_gold_tickets']) ? absint($_POST['event_sold_gold_tickets']) : 0;
	$available_gold = $total_gold - $sold_gold;
	update_post_meta($post_id, '_event_available_gold_tickets', $available_gold);

	// Calculate available Violet tickets
	$sold_violet = isset($_POST['event_sold_violet_tickets']) ? absint($_POST['event_sold_violet_tickets']) : 0;
	$available_violet = $total_violet - $sold_violet;
	update_post_meta($post_id, '_event_available_violet_tickets', $available_violet);

	// Validate that available gold + available violet tickets equal total available tickets
	if ($available_gold + $available_violet != $available_total) {
		// Store the error message in a transient to display it after redirect
		set_transient('event_meta_error_' . $post_id, 'Available Gold tickets + Available Violet tickets have been adjusted to match the total available tickets.', 60);

		// Adjust the values to maintain consistency with the total tickets
		if ($available_total > 0) {
			// Calculate the ratio based on the total tickets
			if ($total_gold + $total_violet > 0) {
				$gold_ratio = $total_gold / ($total_gold + $total_violet);
				$adjusted_gold = round($available_total * $gold_ratio);
			} else {
				// If both are zero, distribute evenly
				$adjusted_gold = floor($available_total / 2);
			}
			$adjusted_violet = $available_total - $adjusted_gold;

			// Update the available tickets
			update_post_meta($post_id, '_event_available_gold_tickets', $adjusted_gold);
			update_post_meta($post_id, '_event_available_violet_tickets', $adjusted_violet);
		} else {
			// If total available is zero, set gold and violet available to zero
			update_post_meta($post_id, '_event_available_gold_tickets', 0);
			update_post_meta($post_id, '_event_available_violet_tickets', 0);
		}
	}

	error_log('Event meta saved for post_id: ' . $post_id);
}
add_action('save_post', 'save_event_meta_data');

function events_admin_styles()
{
	?>
		<style>
			#event_details .inside {
				padding: 20px;
			}

			#event_details label {
				display: block;
				margin-top: 10px;
				font-weight: bold;
			}

			#event_details input[type="text"],
			#event_details input[type="date"],
			#event_details input[type="number"] {
				width: 100%;
				padding: 8px;
				margin-top: 4px;
				box-sizing: border-box;
			}

			#order_details_meta .inside {
				padding: 20px;
			}

			#order_details_meta label {
				display: block;
				margin-top: 10px;
				font-weight: bold;
			}

			#order_details_meta input[type="text"],
			#order_details_meta input[type="email"],
			#order_details_meta input[type="tel"],
			#order_details_meta input[type="number"] {
				width: 100%;
				padding: 8px;
				margin-top: 4px;
				box-sizing: border-box;
			}

			.qr-code-container {
				background-color: white;
				padding: 10px;
				border-radius: 8px;
				box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
				display: inline-block;
				margin: 10px auto;
			}

			.qr-code-container img {
				display: block;
				max-width: 150px;
				height: auto;
				margin: auto;
				border: 1px solid #ddd;
				padding: 10px;
				background-color: #fff;
			}

			.qr-ticket {
				text-align: center;
				margin-bottom: 15px;
			}

			.qr-ticket p {
				margin-top: 5px;
				font-size: 12px;
				color: #333;
			}
		</style>
	<?php
}
add_action('admin_head', 'events_admin_styles');

/************ Register Custom Post Type for Order Details ************/
function create_order_details_post_type()
{
	$labels = array(
		'name'                  => 'Order Details',
		'singular_name'         => 'Order Detail',
		'menu_name'             => 'Order Details',
		'name_admin_bar'        => 'Order Detail',
		'add_new'               => 'Add New',
		'add_new_item'          => 'Add New Order',
		'new_item'              => 'New Order',
		'edit_item'             => 'Edit Order',
		'view_item'             => 'View Order',
		'all_items'             => 'All Orders',
		'search_items'          => 'Search Orders',
		'not_found'             => 'No orders found.',
		'not_found_in_trash'    => 'No orders found in Trash.',
	);

	$args = array(
		'labels'             => $labels,
		'public'             => false,
		'show_ui'            => true,
		'show_in_menu'       => true,
		'menu_icon'          => 'dashicons-tickets',
		'supports'           => array('title'),
		'has_archive'        => false,
		'show_in_rest'       => true,
		'menu_position'      => 7,
	);

	register_post_type('order_details', $args);
	error_log('Order Details post type registered');
}
add_action('init', 'create_order_details_post_type');

function order_details_add_meta_boxes()
{
	add_meta_box(
		'order_details_meta',
		'Order Information',
		'order_details_meta_box_callback',
		'order_details',
		'normal',
		'default'
	);

	// Enqueue QR code scripts for admin
	if (get_current_screen()->id === 'order_details') {
		wp_enqueue_script('qrcode-js', 'https://cdn.jsdelivr.net/npm/qrcode-generator@1.4.4/qrcode.min.js', array(), null, true);
		wp_enqueue_script('qrcode-display', get_template_directory_uri() . '/js/qrcode-display.js', array('qrcode-js'), '1.0', true);
	}
}
add_action('add_meta_boxes', 'order_details_add_meta_boxes');

function order_details_meta_box_callback($post)
{
	$event_id       = get_post_meta($post->ID, '_order_event_id', true);
	$ticket_qty     = get_post_meta($post->ID, '_order_ticket_qty', true);
	$total_amount   = get_post_meta($post->ID, '_order_total_amount', true);
	$payment_status = get_post_meta($post->ID, '_order_payment_status', true);
	$payment_id     = get_post_meta($post->ID, '_order_payment_id', true);
	$customer_name  = get_post_meta($post->ID, '_order_customer_name', true);
	$customer_email = get_post_meta($post->ID, '_order_customer_email', true);
	$customer_phone = get_post_meta($post->ID, '_order_customer_phone', true);
	$customer_age   = get_post_meta($post->ID, '_order_customer_age', true);
	$customer_city  = get_post_meta($post->ID, '_order_customer_city', true);
	$ticket_numbers = get_post_meta($post->ID, '_order_ticket_numbers', true);

	// Get pass type and coupon information
	$pass_type      = get_post_meta($post->ID, '_order_pass_type', true);
	$pass_price     = get_post_meta($post->ID, '_order_pass_price', true);
	$is_early_bird  = get_post_meta($post->ID, '_order_is_early_bird', true) === 'yes';
	$coupon_code    = get_post_meta($post->ID, '_order_coupon_code', true);
	$coupon_discount = get_post_meta($post->ID, '_order_coupon_discount', true);
	$subtotal       = get_post_meta($post->ID, '_order_subtotal', true);

	// Format pass type for display
	$pass_type_display = 'Standard';
	if ($pass_type === 'gold') {
		$pass_type_display = 'Gold Pass';
	} elseif ($pass_type === 'violet') {
		$pass_type_display = 'Violet Pass';
	}

	// Add early bird indicator if applicable
	if ($is_early_bird) {
		$pass_type_display .= ' (Early Bird)';
	}
	?>
		<div>
			<label for="order_event_id">Event ID</label>
			<input type="text" id="order_event_id" name="order_event_id" value="<?php echo esc_attr($event_id); ?>" readonly class="widefat" />
		</div>

		<h3 style="margin-top: 20px; padding-top: 10px; border-top: 1px solid #ddd;">Pass Details</h3>

		<div>
			<label for="order_pass_type">Pass Type</label>
			<input type="text" id="order_pass_type" name="order_pass_type" value="<?php echo esc_attr($pass_type_display); ?>" readonly class="widefat" />
		</div>
		<div>
			<label for="order_ticket_qty">Ticket Quantity</label>
			<input type="number" id="order_ticket_qty" name="order_ticket_qty" value="<?php echo esc_attr($ticket_qty); ?>" readonly class="widefat" />
		</div>
		<div>
			<label for="order_pass_price">Price per Pass (INR)</label>
			<input type="text" id="order_pass_price" name="order_pass_price" value="<?php echo esc_attr(number_format($pass_price, 2)); ?>" readonly class="widefat" />
		</div>

		<?php if (!empty($coupon_code) && !empty($coupon_discount)) : ?>
			<div>
				<label for="order_subtotal">Subtotal (INR)</label>
				<input type="text" id="order_subtotal" name="order_subtotal" value="<?php echo esc_attr(number_format($subtotal, 2)); ?>" readonly class="widefat" />
			</div>
			<div>
				<label for="order_coupon_code">Coupon Code</label>
				<input type="text" id="order_coupon_code" name="order_coupon_code" value="<?php echo esc_attr($coupon_code); ?>" readonly class="widefat" />
			</div>
			<div>
				<label for="order_coupon_discount">Discount Amount (INR)</label>
				<input type="text" id="order_coupon_discount" name="order_coupon_discount" value="<?php echo esc_attr(number_format($coupon_discount, 2)); ?>" readonly class="widefat" />
			</div>
		<?php endif; ?>

		<div>
			<label for="order_total_amount">Total Amount (INR)</label>
			<input type="text" id="order_total_amount" name="order_total_amount" value="<?php echo esc_attr(number_format($total_amount, 2)); ?>" readonly class="widefat" />
		</div>

		<h3 style="margin-top: 20px; padding-top: 10px; border-top: 1px solid #ddd;">Payment Details</h3>

		<div>
			<label for="order_payment_status">Payment Status</label>
			<input type="text" id="order_payment_status" name="order_payment_status" value="<?php echo esc_attr($payment_status); ?>" readonly class="widefat" />
		</div>
		<div>
			<label for="order_payment_id">Payment ID</label>
			<input type="text" id="order_payment_id" name="order_payment_id" value="<?php echo esc_attr($payment_id); ?>" readonly class="widefat" />
		</div>

		<h3 style="margin-top: 20px; padding-top: 10px; border-top: 1px solid #ddd;">Customer Details</h3>

		<div>
			<label for="order_customer_name">Customer Name</label>
			<input type="text" id="order_customer_name" name="order_customer_name" value="<?php echo esc_attr($customer_name); ?>" readonly class="widefat" />
		</div>
		<div>
			<label for="order_customer_email">Customer Email</label>
			<input type="email" id="order_customer_email" name="order_customer_email" value="<?php echo esc_attr($customer_email); ?>" readonly class="widefat" />
		</div>
		<div>
			<label for="order_customer_phone">Customer Phone</label>
			<input type="tel" id="order_customer_phone" name="order_customer_phone" value="<?php echo esc_attr($customer_phone); ?>" readonly class="widefat" />
		</div>
		<div>
			<label for="order_customer_age">Customer Age</label>
			<input type="number" id="order_customer_age" name="order_customer_age" value="<?php echo esc_attr($customer_age); ?>" readonly class="widefat" />
		</div>
		<div>
			<label for="order_customer_city">Customer City</label>
			<input type="text" id="order_customer_city" name="order_customer_city" value="<?php echo esc_attr($customer_city); ?>" readonly class="widefat" />
		</div>

		<h3 style="margin-top: 20px; padding-top: 10px; border-top: 1px solid #ddd;">Ticket QR Codes</h3>

		<?php if (!empty($ticket_numbers) && is_array($ticket_numbers)) : ?>
			<div>
				<div style="display: flex; flex-wrap: wrap; justify-content: left;">
					<div class="qr-ticket">
						<div id="qrcode-admin-<?php echo esc_attr($post->ID); ?>" data-url="https://cosmictreefoundation.com/meet-diana-cooper-in-india/" class="qr-code-container"></div>
						<p>Event QR Code</p>
					</div>
				</div>
			</div>
		<?php else : ?>
			<div>
				<p>No ticket numbers assigned.</p>
			</div>
		<?php endif; ?>
	<?php
}

/************ Enqueue Scripts and Styles ************/
function enqueue_event_booking_scripts()
{
	wp_enqueue_style('tailwind', 'https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css');
	wp_enqueue_script('razorpay', 'https://checkout.razorpay.com/v1/checkout.js', array(), null, true);
	wp_enqueue_script('qrcode-js', 'https://cdn.jsdelivr.net/npm/qrcode-generator@1.4.4/qrcode.min.js', array(), null, true);
	wp_enqueue_script('event-booking', get_template_directory_uri() . '/js/event-booking.js', array('jquery'), '1.1', true);


	// Enqueue order PDF script for order details pages
	if (is_admin() || (isset($_GET['post_type']) && $_GET['post_type'] === 'order_details')) {
		wp_enqueue_script('order-pdf', get_template_directory_uri() . '/js/order-pdf.js', array('jquery'), '1.0', true);
		wp_localize_script('order-pdf', 'ctf_order_pdf', array(
			'ajax_url' => admin_url('admin-ajax.php'),
			'nonce' => wp_create_nonce('ctf_generate_pdf_nonce')
		));
	}

	global $wp_query;
	$event_id = isset($wp_query->query_vars['event_id']) ? absint($wp_query->query_vars['event_id']) : (isset($_GET['event_id']) ? absint($_GET['event_id']) : 0);
	wp_localize_script('event-booking', 'eventBooking', array(
		'ajax_url'     => admin_url('admin-ajax.php'),
		'razorpay_key' => defined('RAZORPAY_KEY') ? RAZORPAY_KEY : '',
		'nonce'        => wp_create_nonce('event_booking_nonce'),
		'event_id'     => $event_id,
	));
}
add_action('wp_enqueue_scripts', 'enqueue_event_booking_scripts');

/************ Create Order and Initiate Payment ************/
function create_order_and_payment()
{
	if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'event_booking_nonce')) {
		error_log('Invalid nonce in create_order_and_payment');
		wp_send_json_error('Security check failed. Please refresh the page and try again.');
	}

	// $user_ip = $_SERVER['REMOTE_ADDR'];
	// $transient_key = 'booking_rate_limit_' . md5($user_ip);
	// $rate_count = get_transient($transient_key);

	// if ($rate_count && $rate_count > 5) {
	// 	error_log('Rate limit exceeded for IP: ' . $user_ip);
	// 	wp_send_json_error('Too many booking attempts. Please try again later.');
	// }

	// if (!$rate_count) {
	// 	set_transient($transient_key, 1, 300);
	// } else {
	// 	set_transient($transient_key, $rate_count + 1, 300);
	// }

	$event_id       = isset($_POST['event_id']) ? absint($_POST['event_id']) : 0;
	$ticket_qty     = isset($_POST['ticket_qty']) ? absint($_POST['ticket_qty']) : 0;
	$customer_name  = isset($_POST['customer_name']) ? sanitize_text_field($_POST['customer_name']) : '';
	$customer_email = isset($_POST['customer_email']) ? sanitize_email($_POST['customer_email']) : '';
	$customer_phone = isset($_POST['customer_phone']) ? sanitize_text_field($_POST['customer_phone']) : '';
	$customer_age   = isset($_POST['customer_age']) ? absint($_POST['customer_age']) : 0;
	$customer_city  = isset($_POST['customer_city']) ? sanitize_text_field($_POST['customer_city']) : '';
	$pass_type      = isset($_POST['pass_type']) ? sanitize_text_field($_POST['pass_type']) : '';
	$pass_price     = isset($_POST['pass_price']) ? floatval($_POST['pass_price']) : 0;
	$coupon_code    = isset($_POST['coupon_code']) ? sanitize_text_field($_POST['coupon_code']) : '';
	$coupon_discount = isset($_POST['coupon_discount']) ? floatval($_POST['coupon_discount']) : 0;
	$total_amount   = isset($_POST['total_amount']) ? floatval($_POST['total_amount']) : 0;

	$errors = array();

	if (!$event_id) {
		$errors[] = 'Event ID is required';
	}

	if (empty($pass_type)) {
		$errors[] = 'Pass type is required';
	} elseif ($pass_type !== 'gold' && $pass_type !== 'violet') {
		$errors[] = 'Invalid pass type selected';
	}

	if (!$ticket_qty) {
		$errors[] = 'Ticket quantity is required';
	} elseif ($ticket_qty <= 0) {
		$errors[] = 'Ticket quantity must be greater than zero';
	} elseif ($ticket_qty > 1) {
		$errors[] = 'Maximum 1 ticket allowed per order';
	}

	if (empty($customer_name)) {
		$errors[] = 'Name is required';
	} elseif (strlen($customer_name) < 3) {
		$errors[] = 'Name must be at least 3 characters long';
	}

	if (empty($customer_email)) {
		$errors[] = 'Email is required';
	} elseif (!is_email($customer_email)) {
		$errors[] = 'Invalid email address';
	}

	if (empty($customer_phone)) {
		$errors[] = 'Phone number is required';
	}

	if (empty($customer_age)) {
		$errors[] = 'Age is required';
	} elseif ($customer_age < 1 || $customer_age > 120) {
		$errors[] = 'Age must be between 1 and 120';
	}

	if (empty($customer_city)) {
		$errors[] = 'City is required';
	} elseif (strlen($customer_city) < 2) {
		$errors[] = 'City name must be at least 2 characters long';
	}

	// Validate coupon code if provided
	if (!empty($coupon_code)) {
		$subtotal = $pass_price * $ticket_qty;

		// Check for 100% discount coupon (MGH100DC)
		if ($coupon_code === 'MGH100DC') {
			// For 100% discount coupon, the discount should equal the subtotal
			$expected_discount = $subtotal;

			// Recalculate the discount to ensure it's correct
			$coupon_discount = $expected_discount;
		}
		// Check for fixed discount coupon (EL1000)
		else if ($coupon_code === 'EL1000') {
			// Fixed discount of ₹1000
			$valid_discount = 1000;

			// Ensure discount doesn't exceed subtotal
			if ($valid_discount > $subtotal) {
				$valid_discount = $subtotal;
			}

			// Recalculate the discount to ensure it's correct
			$coupon_discount = $valid_discount;
		} else {
			$errors[] = 'Invalid coupon code';
		}
	}

	if (!empty($errors)) {
		error_log('Validation errors in create_order_and_payment: ' . implode(', ', $errors));
		wp_send_json_error(implode('<br>', $errors));
	}

	$event = get_post($event_id);
	if (!$event || $event->post_type !== 'events' || $event->post_status !== 'publish') {
		error_log('Invalid event ID or event not published: ' . $event_id . ' (status: ' . ($event ? $event->post_status : 'not found') . ')');
		wp_send_json_error('Invalid event. Please select a valid event.');
	}

	$event_status = get_post_meta($event_id, '_event_status', true);
	if ($event_status === 'cancelled') {
		error_log('Attempted booking for cancelled event: ' . $event_id);
		wp_send_json_error('This event has been cancelled. Please select another event.');
	}

	$event_date = get_post_meta($event_id, '_event_date', true);
	if (!empty($event_date)) {
		$event_timestamp = strtotime($event_date);
		if ($event_timestamp && $event_timestamp < current_time('timestamp')) {
			error_log('Attempted booking for past event: ' . $event_id);
			wp_send_json_error('This event has already taken place. Please select an upcoming event.');
		}
	}

	// Get available tickets (total)
	$available_tickets = absint(get_post_meta($event_id, '_event_available_tickets', true));

	if ($available_tickets <= 0) {
		error_log('No tickets available for event: ' . $event_id);
		wp_send_json_error('Sorry, this event is sold out.');
	}

	if ($ticket_qty > $available_tickets) {
		error_log('Not enough tickets available. Requested: ' . $ticket_qty . ', Available: ' . $available_tickets);
		wp_send_json_error('Only ' . $available_tickets . ' tickets available. Please reduce the quantity.');
	}

	// Check availability by pass type
	if ($pass_type === 'gold') {
		$available_gold = absint(get_post_meta($event_id, '_event_available_gold_tickets', true));
		if ($available_gold <= 0) {
			error_log('No Gold tickets available for event: ' . $event_id);
			wp_send_json_error('Sorry, Gold passes are sold out. Please select a different pass type.');
		}
		if ($ticket_qty > $available_gold) {
			error_log('Not enough Gold tickets available. Requested: ' . $ticket_qty . ', Available: ' . $available_gold);
			wp_send_json_error('Only ' . $available_gold . ' Gold passes available. Please reduce the quantity.');
		}
	} elseif ($pass_type === 'violet') {
		$available_violet = absint(get_post_meta($event_id, '_event_available_violet_tickets', true));
		if ($available_violet <= 0) {
			error_log('No Violet tickets available for event: ' . $event_id);
			wp_send_json_error('Sorry, Violet passes are sold out. Please select a different pass type.');
		}
		if ($ticket_qty > $available_violet) {
			error_log('Not enough Violet tickets available. Requested: ' . $ticket_qty . ', Available: ' . $available_violet);
			wp_send_json_error('Only ' . $available_violet . ' Violet passes available. Please reduce the quantity.');
		}
	}

	// Get pass prices based on type and early bird status
	$early_bird_cutoff = get_post_meta($event_id, '_event_early_bird_cutoff', true);
	if (empty($early_bird_cutoff)) {
		$early_bird_cutoff = defined('EARLY_BIRD_CUTOFF_DATE') ? EARLY_BIRD_CUTOFF_DATE : '2025-07-15';
	}

	$is_early_bird = strtotime($early_bird_cutoff) > current_time('timestamp');

	// Get the appropriate price based on pass type and early bird status
	if ($pass_type === 'gold') {
		if ($is_early_bird) {
			$pass_price = floatval(get_post_meta($event_id, '_event_gold_pass_early_bird', true));
			if (empty($pass_price)) $pass_price = 11110; // Default early bird gold price
		} else {
			$pass_price = floatval(get_post_meta($event_id, '_event_gold_pass_price', true));
			if (empty($pass_price)) $pass_price = 12000; // Default regular gold price
		}
	} elseif ($pass_type === 'violet') {
		if ($is_early_bird) {
			$pass_price = floatval(get_post_meta($event_id, '_event_violet_pass_early_bird', true));
			if (empty($pass_price)) $pass_price = 7770; // Default early bird violet price
		} else {
			$pass_price = floatval(get_post_meta($event_id, '_event_violet_pass_price', true));
			if (empty($pass_price)) $pass_price = 8800; // Default regular violet price
		}
	} else {
		// Fallback to standard price if pass type is invalid
		$pass_price = floatval(get_post_meta($event_id, '_event_price', true));
	}

	// Calculate subtotal
	$subtotal = $pass_price * $ticket_qty;

	// Apply coupon discount if valid
	$discount = 0;
	$is_free_order = false;

	if (!empty($coupon_code)) {
		// Check for 100% discount coupon (MGH100DC)
		if ($coupon_code === 'MGH100DC') {
			// 100% discount coupon
			$discount = $subtotal; // Full amount discount
			$is_free_order = true;
		}
		// Check for fixed discount coupon (EL1000)
		else if ($coupon_code === 'EL1000') {
			// Fixed discount of ₹1000
			$discount = 1000;

			// Ensure discount doesn't exceed subtotal
			if ($discount > $subtotal) {
				$discount = $subtotal;
				$is_free_order = true;
			}
		}
	}

	// Update the coupon_discount value to match our calculated discount
	$coupon_discount = $discount;

	// Calculate final total
	$total_amount = $subtotal - $discount;

	$event_title = get_the_title($event_id);
	$order_id = wp_insert_post(array(
		'post_title'  => 'Order - ' . $event_title . ' (Event ID: ' . $event_id . ')',
		'post_type'   => 'order_details',
		'post_status' => 'publish',
	));

	if (is_wp_error($order_id)) {
		error_log('Failed to create order: ' . $order_id->get_error_message());
		wp_send_json_error('Failed to create order');
	}

	update_post_meta($order_id, '_order_event_id', $event_id);
	update_post_meta($order_id, '_order_ticket_qty', $ticket_qty);
	update_post_meta($order_id, '_order_total_amount', $total_amount);
	update_post_meta($order_id, '_order_payment_status', 'pending');
	update_post_meta($order_id, '_order_customer_name', $customer_name);
	update_post_meta($order_id, '_order_customer_email', $customer_email);
	update_post_meta($order_id, '_order_customer_phone', $customer_phone);
	update_post_meta($order_id, '_order_customer_age', $customer_age);
	update_post_meta($order_id, '_order_customer_city', $customer_city);

	// Store pass type and pricing information
	update_post_meta($order_id, '_order_pass_type', $pass_type);
	update_post_meta($order_id, '_order_pass_price', $pass_price);
	update_post_meta($order_id, '_order_is_early_bird', $is_early_bird ? 'yes' : 'no');

	// Store coupon information if applied
	if (!empty($coupon_code) && $discount > 0) {
		update_post_meta($order_id, '_order_coupon_code', $coupon_code);
		update_post_meta($order_id, '_order_coupon_discount', $discount);
		update_post_meta($order_id, '_order_subtotal', $subtotal);
	}

	// For free orders (100% discount), skip Razorpay integration
	if ($is_free_order || $total_amount <= 0) {
		error_log('Free order created with order_id: ' . $order_id);

		wp_send_json_success(array(
			'order_id'          => $order_id,
			'is_free_order'     => true,
			'amount'            => 0,
			'currency'          => 'INR',
			'customer_name'     => $customer_name,
			'customer_email'    => $customer_email,
			'customer_phone'    => $customer_phone,
			'customer_age'      => $customer_age,
			'customer_city'     => $customer_city,
		));
	}

	// For paid orders, proceed with Razorpay integration
	$api_key = defined('RAZORPAY_KEY') ? RAZORPAY_KEY : '';
	$api_secret = defined('RAZORPAY_SECRET') ? RAZORPAY_SECRET : '';
	if (!$api_key || !$api_secret) {
		wp_delete_post($order_id, true);
		wp_send_json_error('Payment gateway not configured');
	}

	try {
		$api = new Razorpay\Api\Api($api_key, $api_secret);
		$razorpay_order = $api->order->create(array(
			'amount'   => $total_amount * 100, // In paise
			'currency' => 'INR',
			'receipt'  => 'order_' . $order_id,
		));

		update_post_meta($order_id, '_razorpay_order_id', $razorpay_order['id']);
		error_log('Razorpay order created: ' . $razorpay_order['id']);

		wp_send_json_success(array(
			'order_id'          => $order_id,
			'razorpay_order_id' => $razorpay_order['id'],
			'is_free_order'     => false,
			'amount'            => $total_amount * 100,
			'currency'          => 'INR',
			'customer_name'     => $customer_name,
			'customer_email'    => $customer_email,
			'customer_phone'    => $customer_phone,
			'customer_age'      => $customer_age,
			'customer_city'     => $customer_city,
		));
	} catch (Exception $e) {
		wp_delete_post($order_id, true);
		error_log('Razorpay order creation failed: ' . $e->getMessage());
		wp_send_json_error('Payment initiation failed: ' . $e->getMessage());
	}
}
add_action('wp_ajax_create_order', 'create_order_and_payment');
add_action('wp_ajax_nopriv_create_order', 'create_order_and_payment');

/************ Generate Unique Ticket Numbers ************/
function generate_unique_ticket_numbers($event_id, $ticket_qty, $order_id)
{
	$ticket_numbers = array();
	$existing_tickets = get_post_meta($event_id, '_event_ticket_numbers', true);
	$existing_tickets = $existing_tickets ? (array) $existing_tickets : array();

	// Get pass type for this order
	$pass_type = get_post_meta($order_id, '_order_pass_type', true);

	// Get total gold tickets for the event
	$total_gold_tickets = absint(get_post_meta($event_id, '_event_total_gold_tickets', true));

	// Find the highest existing ticket number for each type
	$highest_gold_number = 250000;
	$highest_violet_number = 250000 + $total_gold_tickets;

	// Scan existing tickets to find the highest numbers
	foreach ($existing_tickets as $ticket) {
		if (strpos($ticket, 'DIANA_GOLD_') === 0) {
			// Extract the number part from the ticket
			$number_part = intval(str_replace('DIANA_GOLD_', '', $ticket));
			if ($number_part > $highest_gold_number) {
				$highest_gold_number = $number_part;
			}
		} elseif (strpos($ticket, 'DIANA_VIOLET_') === 0) {
			// Extract the number part from the ticket
			$number_part = intval(str_replace('DIANA_VIOLET_', '', $ticket));
			if ($number_part > $highest_violet_number) {
				$highest_violet_number = $number_part;
			}
		}
	}

	// Generate ticket numbers based on pass type
	for ($i = 0; $i < $ticket_qty; $i++) {
		if ($pass_type === 'gold') {
			// Gold pass ticket format: DIANA_GOLD_250001 and up
			// Start from 250001 or the next number after the highest existing gold ticket
			$next_number = max(250001, $highest_gold_number + 1);
			$ticket_number = 'DIANA_GOLD_' . $next_number;
			$highest_gold_number = $next_number; // Update for the next iteration
		} else {
			// Violet pass ticket format: DIANA_VIOLET_250[total gold tickets + 1] and up
			// Start from (250000 + total_gold_tickets + 1) or the next number after the highest existing violet ticket
			$base_number = 250000 + $total_gold_tickets + 1;
			$next_number = max($base_number, $highest_violet_number + 1);
			$ticket_number = 'DIANA_VIOLET_' . $next_number;
			$highest_violet_number = $next_number; // Update for the next iteration
		}

		// Add the ticket number to our arrays
		$ticket_numbers[] = $ticket_number;
		$existing_tickets[] = $ticket_number;
	}

	update_post_meta($event_id, '_event_ticket_numbers', $existing_tickets);
	update_post_meta($order_id, '_order_ticket_numbers', $ticket_numbers);

	error_log('Generated ticket numbers for order_id: ' . $order_id . ' - ' . implode(', ', $ticket_numbers));
	return $ticket_numbers;
}

/************ Generate Email Template Using Order Confirmation Design ************/
function generate_order_confirmation_email_template($order_id, $event_id, $ticket_numbers)
{
	// Get all the same data as order-confirmation.php
	$order = get_post($order_id);

	if (!$order || $order->post_type !== 'order_details') {
		return 'Invalid order';
	}

	// Get order data (same as order-confirmation.php)
	$ticket_qty      = get_post_meta($order_id, '_order_ticket_qty', true);
	$total_amount    = get_post_meta($order_id, '_order_total_amount', true);
	$payment_id      = get_post_meta($order_id, '_order_payment_id', true);
	$customer_name   = get_post_meta($order_id, '_order_customer_name', true);
	$customer_email  = get_post_meta($order_id, '_order_customer_email', true);
	$customer_phone  = get_post_meta($order_id, '_order_customer_phone', true);
	$customer_city   = get_post_meta($order_id, '_order_customer_city', true);
	$customer_age    = get_post_meta($order_id, '_order_customer_age', true);

	// Get pass type and coupon information
	$pass_type       = get_post_meta($order_id, '_order_pass_type', true);
	$pass_price      = get_post_meta($order_id, '_order_pass_price', true);
	$is_early_bird   = get_post_meta($order_id, '_order_is_early_bird', true) === 'yes';
	$coupon_code     = get_post_meta($order_id, '_order_coupon_code', true);
	$coupon_discount = get_post_meta($order_id, '_order_coupon_discount', true);
	$subtotal        = get_post_meta($order_id, '_order_subtotal', true);

	// Get event details
	$event_date      = get_post_meta($event_id, '_event_date', true);
	$event_venue     = get_post_meta($event_id, '_event_venue', true);

	// Get payment details
	$order_paid_at = get_post_meta($order_id, '_order_paid_at', true);
	$order_completed_at = get_post_meta($order_id, '_order_completed_at', true);
	$payment_method = get_post_meta($order_id, '_order_payment_method', true);

	// Format payment details
	$formatted_paid_at = !empty($order_paid_at) ? date('d/m/Y H:i', strtotime($order_paid_at)) : date('d/m/Y H:i');
	$formatted_completed_at = !empty($order_completed_at) ? date('d/m/Y H:i', strtotime($order_completed_at)) : date('d/m/Y H:i');
	$formatted_payment_method = !empty($payment_method) ? ucfirst($payment_method) : 'Online Payment';

	// Process template placeholders (same as order-confirmation.php)
	$ticket_number = !empty($ticket_numbers) && is_array($ticket_numbers) ? $ticket_numbers[0] : 'ticket_' . strtoupper(substr(md5($order_id), 0, 10));

	// Format event date
	$formatted_event_date = !empty($event_date) ? date('jS F Y', strtotime($event_date)) : '11th September';

	// Format pass type display
	$pass_type_display = '';

	// Add pricing type - always show either Early Bird or Regular
	if ($is_early_bird) {
		$pass_type_display .= ' (Early Bird)';
	} else {
		$pass_type_display .= ' (Regular)';
	}

	// Determine which template to use (same logic as order-confirmation.php)
	$template_file = ($pass_type === 'gold') ? 'gold-ticket.html' : 'violet-ticket.html';
	$template_path = get_template_directory() . '/ticket-confirmation-pages/' . $template_file;

	// Load the template content (same as order-confirmation.php)
	if (file_exists($template_path)) {
		$template_content = file_get_contents($template_path);
	} else {
		// Fallback if template file doesn't exist
		error_log('Email template file not found: ' . $template_path);
		return 'Template not found';
	}

	// Replace placeholders in template (same as order-confirmation.php)
	$replacements = array(
		'[TEMPLATE_DIRECTORY_URI]' => get_template_directory_uri(),
		'[SITE_URL]' => home_url('/meet-diana-cooper-in-india/'),
		'[HOME_URL]' => home_url(),
		'[TICKET_NUMBER]' => esc_html($ticket_number),
		'[EVENT_DATE]' => esc_html($formatted_event_date),
		'[CUSTOMER_NAME]' => esc_html($customer_name),
		'[CUSTOMER_EMAIL]' => esc_html($customer_email),
		'[CUSTOMER_CITY]' => esc_html($customer_city ?: 'Not provided'),
		'[CUSTOMER_AGE]' => esc_html($customer_age ?: 'Not provided'),
		'[CUSTOMER_PHONE]' => esc_html($customer_phone),
		'[ORDER_PAID_AT]' => esc_html($formatted_paid_at),
		'[ORDER_COMPLETED_AT]' => esc_html($formatted_completed_at),
		'[PAYMENT_METHOD]' => esc_html($formatted_payment_method),
		'[PAYMENT_ID]' => esc_html($payment_id),
		'[PASS_TYPE_DISPLAY]' => $pass_type_display,
		'[TICKET_PRICE]' => esc_html(number_format($total_amount, 2)),
		'[TICKET_QTY]' => esc_html($ticket_qty)
	);

	// Apply replacements (same as order-confirmation.php)
	$processed_template = str_replace(array_keys($replacements), array_values($replacements), $template_content);

	return $processed_template;
}


/************ Send Booking Emails ************/
function send_booking_emails($order_id, $event_id, $ticket_numbers)
{
	// Retrieve order details
	$customer_name = get_post_meta($order_id, '_order_customer_name', true);
	$customer_email = get_post_meta($order_id, '_order_customer_email', true);
	$customer_phone = get_post_meta($order_id, '_order_customer_phone', true);
	$customer_age = get_post_meta($order_id, '_order_customer_age', true);
	$customer_city = get_post_meta($order_id, '_order_customer_city', true);
	$ticket_qty = get_post_meta($order_id, '_order_ticket_qty', true);
	$total_amount = get_post_meta($order_id, '_order_total_amount', true);
	$payment_id = get_post_meta($order_id, '_order_payment_id', true);

	// Get pass type and coupon information
	$pass_type = get_post_meta($order_id, '_order_pass_type', true);
	$pass_price = get_post_meta($order_id, '_order_pass_price', true);
	$is_early_bird = get_post_meta($order_id, '_order_is_early_bird', true) === 'yes';
	$coupon_code = get_post_meta($order_id, '_order_coupon_code', true);
	$coupon_discount = get_post_meta($order_id, '_order_coupon_discount', true);
	$subtotal = get_post_meta($order_id, '_order_subtotal', true);

	// Format pass type for display
	$pass_type_display = 'Standard Pass';
	if ($pass_type === 'gold') {
		$pass_type_display = 'Gold Pass';
	} elseif ($pass_type === 'violet') {
		$pass_type_display = 'Violet Pass';
	}

	// Add early bird indicator if applicable
	if ($is_early_bird) {
		$pass_type_display .= ' (Early Bird)';
	}

	// Retrieve event details
	$event = get_post($event_id);
	$event_title = $event->post_title;
	$event_venue = get_post_meta($event_id, '_event_venue', true);
	$event_date = get_post_meta($event_id, '_event_date', true);

	// Format ticket numbers
	$ticket_numbers_list = implode(', ', $ticket_numbers);

	// Image URLs from media library
	$logo_url = wp_get_attachment_url(637); // Logo attachment ID

	// Fallbacks if images are not found
	$logo_url = $logo_url ?: home_url('/wp-content/uploads/2023/07/logo-cosmic.png');


	// Generate QR codes for user email
	$qr_code_images = array();
	if (!empty($ticket_numbers) && is_array($ticket_numbers)) {
		try {
			$writer = new PngWriter();
			// Generate one QR code with the URL instead of ticket numbers
			$qrCode = QrCode::create('https://cosmictreefoundation.com/meet-diana-cooper-in-india/')
				->setSize(150)
				->setMargin(10);
			$result = $writer->write($qrCode);
			$base64_image = base64_encode($result->getString());
			$qr_code_images['event_url'] = 'data:image/png;base64,' . $base64_image;
		} catch (Exception $e) {
			error_log('Failed to generate QR code for order_id: ' . $order_id . ' - ' . $e->getMessage());
			$qr_code_images = array();
		}
	}

	// Email subject
	$user_subject = 'Your Ticket: ' . $event_title;
	$admin_subject = 'New Booking: ' . $customer_name;

	// Use the new beautiful template for user email
	$user_message = generate_order_confirmation_email_template($order_id, $event_id, $ticket_numbers);

	// Email body for admin
	$admin_message = '
    <html>
    <head>
        <style>
            body { font-family: Arial, sans-serif; color: #333; line-height: 1.6; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; background-color: #f9f9f9; }
            .header { text-align: center; padding: 20px 0; }
            .header img { max-width: 200px; } /* Increased logo size */
            .content { background-color: #fff; padding: 20px; border-radius: 8px; }
            .footer { text-align: center; padding: 20px 0; color: #777; font-size: 12px; }
            .footer img { max-width: 80px; margin-top: 10px; } /* Reduced size for admin email */
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
           <a href="' . esc_url(home_url()) . '">
                <img src="' . esc_url($logo_url) . '" alt="Event Logo">
            </a>
            </div>
            <div class="content">
                <h2 style="color: #38A169;">New Booking Notification</h2>
                <p>A new booking has been made with the following details:</p>
                <table style="width: 100%; border-collapse: collapse; margin: 20px 0;">
                    <tr><td style="padding: 8px; font-weight: bold;">Customer Name:</td><td style="padding: 8px;">' . esc_html($customer_name) . '</td></tr>
                    <tr><td style="padding: 8px; font-weight: bold;">Customer Email:</td><td style="padding: 8px;">' . esc_html($customer_email) . '</td></tr>
                    <tr><td style="padding: 8px; font-weight: bold;">Customer Phone:</td><td style="padding: 8px;">' . esc_html($customer_phone) . '</td></tr>
                    <tr><td style="padding: 8px; font-weight: bold;">Customer Age:</td><td style="padding: 8px;">' . esc_html($customer_age) . '</td></tr>
                    <tr><td style="padding: 8px; font-weight: bold;">Customer City:</td><td style="padding: 8px;">' . esc_html($customer_city) . '</td></tr>
                    <tr><td style="padding: 8px; font-weight: bold;">Event:</td><td style="padding: 8px;">' . esc_html($event_title) . '</td></tr>
                    <tr><td style="padding: 8px; font-weight: bold;">Venue:</td><td style="padding: 8px;">' . esc_html($event_venue) . '</td></tr>
                    <tr><td style="padding: 8px; font-weight: bold;">Date:</td><td style="padding: 8px;">' . esc_html($event_date) . '</td></tr>
                    <tr><td style="padding: 8px; font-weight: bold;">Pass Type:</td><td style="padding: 8px;">' . esc_html($pass_type_display) . '</td></tr>
                    <tr><td style="padding: 8px; font-weight: bold;">Price per Pass:</td><td style="padding: 8px;">₹' . esc_html(number_format($pass_price, 2)) . '</td></tr>
                    <tr><td style="padding: 8px; font-weight: bold;">Ticket Quantity:</td><td style="padding: 8px;">' . esc_html($ticket_qty) . '</td></tr>';

	if (!empty($coupon_code) && !empty($coupon_discount) && !empty($subtotal)) {
		$admin_message .= '
                    <tr><td style="padding: 8px; font-weight: bold;">Subtotal:</td><td style="padding: 8px;">₹' . esc_html(number_format($subtotal, 2)) . '</td></tr>
                    <tr><td style="padding: 8px; font-weight: bold;">Coupon Applied:</td><td style="padding: 8px;">' . esc_html($coupon_code) . '</td></tr>
                    <tr><td style="padding: 8px; font-weight: bold;">Discount:</td><td style="padding: 8px;">-₹' . esc_html(number_format($coupon_discount, 2)) . '</td></tr>';
	}

	$admin_message .= '
                    <tr><td style="padding: 8px; font-weight: bold;">Total Amount:</td><td style="padding: 8px;">₹' . esc_html(number_format($total_amount, 2)) . '</td></tr>
                    <tr><td style="padding: 8px; font-weight: bold;">Ticket Numbers:</td><td style="padding: 8px;">' . esc_html($ticket_numbers_list) . '</td></tr>
                    <tr><td style="padding: 8px; font-weight: bold;">Payment ID:</td><td style="padding: 8px;">' . esc_html($payment_id) . '</td></tr>
                    <tr><td style="padding: 8px; font-weight: bold;">Order ID:</td><td style="padding: 8px;">' . esc_html($order_id) . '</td></tr>
                </table>
            </div>
           <div class="footer">
            <p>Best regards,<br>Cosmictreefoundation</p>
        	 </div>
        </div>
    </body>
    </html>';

	// Get dynamic site information
	$site_name = get_bloginfo('name');
	$admin_email = get_option('admin_email');
	$site_domain = parse_url(home_url(), PHP_URL_HOST);

	// Create dynamic from email using site domain
	$from_email = 'no-reply@' . $site_domain;

	// Email headers with dynamic values
	$headers = array(
		'Content-Type: text/html; charset=UTF-8',
		'From: ' . $site_name . ' Event Team <' . $from_email . '>',
		'Reply-To: ' . $admin_email,
	);

	// Send email to user
	$user_sent = wp_mail($customer_email, $user_subject, $user_message, $headers);
	if ($user_sent) {
		error_log('User email sent for order_id: ' . $order_id);
	} else {
		error_log('Failed to send user email for order_id: ' . $order_id);
	}

	// Send email to admin
	// $admin_email = '<EMAIL>';
	// $admin_email = '<EMAIL>';
	// $admin_email = '<EMAIL>';
	$admin_email = get_option('admin_email');
	// $admin_email = '<EMAIL>';
	$admin_sent = wp_mail($admin_email, $admin_subject, $admin_message, $headers);
	if ($admin_sent) {
		error_log('Admin email sent for order_id: ' . $order_id);
	} else {
		error_log('Failed to send admin email for order_id: ' . $order_id);
	}
}

/************ Verify Payment ************/
function verify_payment()
{
	if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'event_booking_nonce')) {
		error_log('Invalid nonce in verify_payment');
		wp_send_json_error(array(
			'message' => 'Security check failed. Please refresh the page and try again.',
			'redirect' => home_url('/order-failed')
		));
	}

	$order_id            = isset($_POST['order_id']) ? absint($_POST['order_id']) : 0;
	$razorpay_payment_id = isset($_POST['razorpay_payment_id']) ? sanitize_text_field($_POST['razorpay_payment_id']) : '';
	$razorpay_order_id   = isset($_POST['razorpay_order_id']) ? sanitize_text_field($_POST['razorpay_order_id']) : '';
	$razorpay_signature  = isset($_POST['razorpay_signature']) ? sanitize_text_field($_POST['razorpay_signature']) : '';

	if (!$order_id || !$razorpay_payment_id || !$razorpay_order_id || !$razorpay_signature) {
		error_log('Missing payment details in verify_payment');
		wp_send_json_error(array(
			'message' => 'Missing payment details. Please try again.',
			'redirect' => home_url('/order-failed?order_id=' . $order_id)
		));
	}

	$order = get_post($order_id);
	if (!$order || $order->post_type !== 'order_details') {
		error_log('Invalid order ID in verify_payment: ' . $order_id);
		wp_send_json_error(array(
			'message' => 'Invalid order. Please try again.',
			'redirect' => home_url('/order-failed')
		));
	}

	$payment_status = get_post_meta($order_id, '_order_payment_status', true);
	if ($payment_status === 'completed') {
		$processed_via = get_post_meta($order_id, '_order_processed_via', true);
		error_log('Payment already completed for order_id: ' . $order_id . ' (processed via: ' . $processed_via . ')');
		wp_send_json_success(array(
			'message' => 'Payment was already processed.',
			'redirect' => home_url('/order-confirmation?order_id=' . $order_id)
		));
	}

	$api_key = defined('RAZORPAY_KEY') ? RAZORPAY_KEY : '';
	$api_secret = defined('RAZORPAY_SECRET') ? RAZORPAY_SECRET : '';
	if (!$api_key || !$api_secret) {
		error_log('Payment gateway not configured in verify_payment');
		wp_send_json_error(array(
			'message' => 'Payment gateway not configured. Please contact support.',
			'redirect' => home_url('/order-failed?order_id=' . $order_id)
		));
	}

	try {
		$api = new Razorpay\Api\Api($api_key, $api_secret);
		$attributes = array(
			'razorpay_order_id'   => $razorpay_order_id,
			'razorpay_payment_id' => $razorpay_payment_id,
			'razorpay_signature'  => $razorpay_signature,
		);

		$api->utility->verifyPaymentSignature($attributes);

		$payment = $api->payment->fetch($razorpay_payment_id);

		$order_total = floatval(get_post_meta($order_id, '_order_total_amount', true)) * 100;
		if ($payment->amount != $order_total) {
			error_log('Payment amount mismatch for order_id: ' . $order_id . '. Expected: ' . $order_total . ', Got: ' . $payment->amount);
			update_post_meta($order_id, '_order_payment_status', 'failed');
			update_post_meta($order_id, '_order_payment_error', 'Payment amount mismatch');
			wp_send_json_error(array(
				'message' => 'Payment verification failed. Amount mismatch.',
				'redirect' => home_url('/order-failed?order_id=' . $order_id)
			));
		}

		if ($payment->status !== 'captured' && $payment->status !== 'authorized') {
			error_log('Invalid payment status for order_id: ' . $order_id . '. Status: ' . $payment->status);
			update_post_meta($order_id, '_order_payment_status', 'failed');
			update_post_meta($order_id, '_order_payment_error', 'Invalid payment status: ' . $payment->status);
			wp_send_json_error(array(
				'message' => 'Payment verification failed. Invalid payment status.',
				'redirect' => home_url('/order-failed?order_id=' . $order_id)
			));
		}

		$event_id      = get_post_meta($order_id, '_order_event_id', true);
		$ticket_qty    = get_post_meta($order_id, '_order_ticket_qty', true);

		// Update payment status and trigger reconciliation if needed
		$old_status = get_post_meta($order_id, '_order_payment_status', true);
		update_post_meta($order_id, '_order_payment_status', 'completed');
		update_post_meta($order_id, '_order_payment_id', $razorpay_payment_id);
		update_post_meta($order_id, '_order_payment_method', $payment->method);
		update_post_meta($order_id, '_order_payment_time', current_time('mysql'));
		update_post_meta($order_id, '_order_processed_via', 'frontend'); // Mark as processed via frontend verification

		// If status changed from non-completed to completed, schedule reconciliation
		if ($old_status !== 'completed') {
			wp_schedule_single_event(time() + 30, 'reconcile_ticket_counts', array($event_id));
		}

		$event = get_post($event_id);
		if (!$event || $event->post_type !== 'events') {
			error_log('Invalid event ID in verify_payment: ' . $event_id);
			wp_send_json_error(array(
				'message' => 'Invalid event. Please contact support.',
				'redirect' => home_url('/order-failed?order_id=' . $order_id)
			));
		}

		// Get pass type
		$pass_type = get_post_meta($order_id, '_order_pass_type', true);

		// Use atomic ticket count update
		try {
			$result = update_ticket_counts_atomically($event_id, $ticket_qty, $pass_type);
			if (!$result) {
				error_log('Atomic ticket count update failed for order_id: ' . $order_id);
				update_post_meta($order_id, '_order_payment_status', 'refund_required');
				update_post_meta($order_id, '_order_payment_error', 'Ticket count update failed');
				wp_send_json_error(array(
					'message' => 'Not enough tickets available. A refund will be processed.',
					'redirect' => home_url('/order-failed?order_id=' . $order_id . '&reason=sold_out')
				));
			}
		} catch (Exception $e) {
			error_log('Exception during atomic ticket count update for order_id: ' . $order_id . ' - ' . $e->getMessage());
			update_post_meta($order_id, '_order_payment_status', 'refund_required');
			update_post_meta($order_id, '_order_payment_error', 'Ticket count update failed: ' . $e->getMessage());
			wp_send_json_error(array(
				'message' => 'Payment verification failed. Please try again.',
				'redirect' => home_url('/order-failed?order_id=' . $order_id)
			));
		}

		// Check if tickets were already generated (e.g., by webhook)
		$existing_tickets = get_post_meta($order_id, '_order_ticket_numbers', true);
		if (empty($existing_tickets)) {
			$ticket_numbers = generate_unique_ticket_numbers($event_id, $ticket_qty, $order_id);
			send_booking_emails($order_id, $event_id, $ticket_numbers);
		} else {
			error_log('Tickets already generated for order_id: ' . $order_id . ' (likely processed via webhook), skipping ticket generation');
			$ticket_numbers = $existing_tickets;
		}

		error_log('Payment verified successfully for order_id: ' . $order_id . ', payment_id: ' . $razorpay_payment_id);

		wp_send_json_success(array(
			'message' => 'Payment successful!',
			'redirect' => home_url('/order-confirmation?order_id=' . $order_id),
		));
	} catch (Exception $e) {
		update_post_meta($order_id, '_order_payment_status', 'failed');
		update_post_meta($order_id, '_order_payment_error', $e->getMessage());

		error_log('Payment verification failed for order_id: ' . $order_id . ' - ' . $e->getMessage());

		wp_send_json_error(array(
			'message' => 'Payment verification failed: ' . $e->getMessage(),
			'redirect' => home_url('/order-failed?order_id=' . $order_id),
		));
	}
}
add_action('wp_ajax_verify_payment', 'verify_payment');
add_action('wp_ajax_nopriv_verify_payment', 'verify_payment');

/************ Process Free Order (100% Discount) ************/
function process_free_order()
{
	if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'event_booking_nonce')) {
		error_log('Invalid nonce in process_free_order');
		wp_send_json_error(array(
			'message' => 'Security check failed. Please refresh the page and try again.',
			'redirect' => home_url('/order-failed')
		));
	}

	$order_id = isset($_POST['order_id']) ? absint($_POST['order_id']) : 0;

	if (!$order_id) {
		error_log('Missing order ID in process_free_order');
		wp_send_json_error(array(
			'message' => 'Missing order details. Please try again.',
			'redirect' => home_url('/order-failed')
		));
	}

	$order = get_post($order_id);
	if (!$order || $order->post_type !== 'order_details') {
		error_log('Invalid order ID in process_free_order: ' . $order_id);
		wp_send_json_error(array(
			'message' => 'Invalid order. Please try again.',
			'redirect' => home_url('/order-failed')
		));
	}

	$payment_status = get_post_meta($order_id, '_order_payment_status', true);
	if ($payment_status === 'completed') {
		error_log('Order already completed for order_id: ' . $order_id);
		wp_send_json_success(array(
			'message' => 'Order was already processed.',
			'redirect' => home_url('/order-confirmation?order_id=' . $order_id)
		));
	}

	// Get order details
	$event_id = get_post_meta($order_id, '_order_event_id', true);
	$ticket_qty = get_post_meta($order_id, '_order_ticket_qty', true);
	$total_amount = get_post_meta($order_id, '_order_total_amount', true);
	$coupon_code = get_post_meta($order_id, '_order_coupon_code', true);

	// Verify this is a free order
	if ($total_amount > 0) {
		error_log('Attempted to process a non-free order as free: ' . $order_id);
		wp_send_json_error(array(
			'message' => 'This order requires payment. Please try again.',
			'redirect' => home_url('/order-failed?order_id=' . $order_id)
		));
	}

	// Verify the event exists
	$event = get_post($event_id);
	if (!$event || $event->post_type !== 'events') {
		error_log('Invalid event ID in process_free_order: ' . $event_id);
		wp_send_json_error(array(
			'message' => 'Invalid event. Please contact support.',
			'redirect' => home_url('/order-failed?order_id=' . $order_id)
		));
	}

	// Get pass type
	$pass_type = get_post_meta($order_id, '_order_pass_type', true);

	// Use atomic ticket count update
	try {
		$result = update_ticket_counts_atomically($event_id, $ticket_qty, $pass_type);
		if (!$result) {
			error_log('Atomic ticket count update failed for order_id: ' . $order_id);
			update_post_meta($order_id, '_order_payment_status', 'failed');
			update_post_meta($order_id, '_order_payment_error', 'Ticket count update failed');
			wp_send_json_error(array(
				'message' => 'Not enough tickets available.',
				'redirect' => home_url('/order-failed?order_id=' . $order_id . '&reason=sold_out')
			));
		}
	} catch (Exception $e) {
		error_log('Exception during atomic ticket count update for order_id: ' . $order_id . ' - ' . $e->getMessage());
		update_post_meta($order_id, '_order_payment_status', 'failed');
		update_post_meta($order_id, '_order_payment_error', 'Ticket count update failed: ' . $e->getMessage());
		wp_send_json_error(array(
			'message' => 'Order processing failed. Please try again.',
			'redirect' => home_url('/order-failed?order_id=' . $order_id)
		));
	}

	// Mark the order as completed
	update_post_meta($order_id, '_order_payment_status', 'completed');
	update_post_meta($order_id, '_order_payment_id', 'FREE-' . time()); // Generate a unique ID for free orders
	update_post_meta($order_id, '_order_payment_method', 'coupon');
	update_post_meta($order_id, '_order_payment_time', current_time('mysql'));

	// Generate ticket numbers
	$ticket_numbers = generate_unique_ticket_numbers($event_id, $ticket_qty, $order_id);

	// Send confirmation emails
	send_booking_emails($order_id, $event_id, $ticket_numbers);

	error_log('Free order processed successfully for order_id: ' . $order_id . ', coupon: ' . $coupon_code);

	wp_send_json_success(array(
		'message' => 'Order processed successfully!',
		'redirect' => home_url('/order-confirmation?order_id=' . $order_id),
	));
}
add_action('wp_ajax_process_free_order', 'process_free_order');
add_action('wp_ajax_nopriv_process_free_order', 'process_free_order');

/************ Atomic Ticket Count Update Functions ************/
/**
 * Update ticket counts atomically to prevent race conditions
 *
 * @param int $event_id Event ID
 * @param int $ticket_qty Number of tickets to add
 * @param string $pass_type Pass type (gold or violet)
 * @return bool Success status
 */
function update_ticket_counts_atomically($event_id, $ticket_qty, $pass_type)
{
	global $wpdb;

	// Start transaction
	$wpdb->query('START TRANSACTION');

	try {
		// Use direct SQL updates for atomicity instead of update_post_meta
		$event_meta_table = $wpdb->postmeta;

		// Lock the event meta rows to prevent concurrent updates
		$wpdb->query($wpdb->prepare(
			"SELECT meta_value FROM {$event_meta_table} WHERE post_id = %d AND meta_key IN ('_event_sold_tickets', '_event_sold_gold_tickets', '_event_sold_violet_tickets') FOR UPDATE",
			$event_id
		));

		// Get current ticket counts using direct SQL for consistency
		$sold_tickets = absint($wpdb->get_var($wpdb->prepare(
			"SELECT meta_value FROM {$event_meta_table} WHERE post_id = %d AND meta_key = '_event_sold_tickets'",
			$event_id
		)));
		$total_tickets = absint($wpdb->get_var($wpdb->prepare(
			"SELECT meta_value FROM {$event_meta_table} WHERE post_id = %d AND meta_key = '_event_total_tickets'",
			$event_id
		)));
		$available_tickets = $total_tickets - $sold_tickets;

		// Check total availability
		if ($ticket_qty > $available_tickets) {
			$wpdb->query('ROLLBACK');
			error_log("Not enough total tickets available. Requested: {$ticket_qty}, Available: {$available_tickets}");
			return false;
		}

		// Check pass type specific availability and update using direct SQL
		if ($pass_type === 'gold') {
			$sold_gold = absint($wpdb->get_var($wpdb->prepare(
				"SELECT meta_value FROM {$event_meta_table} WHERE post_id = %d AND meta_key = '_event_sold_gold_tickets'",
				$event_id
			)));
			$total_gold = absint($wpdb->get_var($wpdb->prepare(
				"SELECT meta_value FROM {$event_meta_table} WHERE post_id = %d AND meta_key = '_event_total_gold_tickets'",
				$event_id
			)));
			$available_gold = $total_gold - $sold_gold;

			if ($ticket_qty > $available_gold) {
				$wpdb->query('ROLLBACK');
				error_log("Not enough Gold tickets available. Requested: {$ticket_qty}, Available: {$available_gold}");
				return false;
			}

			// Update Gold ticket counts using direct SQL
			$new_sold_gold = $sold_gold + $ticket_qty;
			$new_available_gold = $total_gold - $new_sold_gold;

			$wpdb->update(
				$event_meta_table,
				array('meta_value' => $new_sold_gold),
				array('post_id' => $event_id, 'meta_key' => '_event_sold_gold_tickets'),
				array('%d'),
				array('%d', '%s')
			);
			$wpdb->update(
				$event_meta_table,
				array('meta_value' => $new_available_gold),
				array('post_id' => $event_id, 'meta_key' => '_event_available_gold_tickets'),
				array('%d'),
				array('%d', '%s')
			);
		} elseif ($pass_type === 'violet') {
			$sold_violet = absint($wpdb->get_var($wpdb->prepare(
				"SELECT meta_value FROM {$event_meta_table} WHERE post_id = %d AND meta_key = '_event_sold_violet_tickets'",
				$event_id
			)));
			$total_violet = absint($wpdb->get_var($wpdb->prepare(
				"SELECT meta_value FROM {$event_meta_table} WHERE post_id = %d AND meta_key = '_event_total_violet_tickets'",
				$event_id
			)));
			$available_violet = $total_violet - $sold_violet;

			if ($ticket_qty > $available_violet) {
				$wpdb->query('ROLLBACK');
				error_log("Not enough Violet tickets available. Requested: {$ticket_qty}, Available: {$available_violet}");
				return false;
			}

			// Update Violet ticket counts using direct SQL
			$new_sold_violet = $sold_violet + $ticket_qty;
			$new_available_violet = $total_violet - $new_sold_violet;

			$wpdb->update(
				$event_meta_table,
				array('meta_value' => $new_sold_violet),
				array('post_id' => $event_id, 'meta_key' => '_event_sold_violet_tickets'),
				array('%d'),
				array('%d', '%s')
			);
			$wpdb->update(
				$event_meta_table,
				array('meta_value' => $new_available_violet),
				array('post_id' => $event_id, 'meta_key' => '_event_available_violet_tickets'),
				array('%d'),
				array('%d', '%s')
			);
		}

		// Update total ticket counts using direct SQL
		$new_sold_total = $sold_tickets + $ticket_qty;
		$new_available_total = $total_tickets - $new_sold_total;

		$wpdb->update(
			$event_meta_table,
			array('meta_value' => $new_sold_total),
			array('post_id' => $event_id, 'meta_key' => '_event_sold_tickets'),
			array('%d'),
			array('%d', '%s')
		);
		$wpdb->update(
			$event_meta_table,
			array('meta_value' => $new_available_total),
			array('post_id' => $event_id, 'meta_key' => '_event_available_tickets'),
			array('%d'),
			array('%d', '%s')
		);

		// Commit transaction first
		$wpdb->query('COMMIT');

		// Clear cache after successful commit
		wp_cache_delete($event_id, 'post_meta');

		error_log("Atomic ticket count update successful for event {$event_id}: {$ticket_qty} {$pass_type} tickets");

		// Schedule automatic reconciliation check
		wp_schedule_single_event(time() + 60, 'reconcile_ticket_counts', array($event_id));

		return true;
	} catch (Exception $e) {
		$wpdb->query('ROLLBACK');
		error_log("Atomic ticket count update failed for event {$event_id}: " . $e->getMessage());
		return false;
	}
}

/**
 * Check ticket availability in real-time with cache clearing
 *
 * @param int $event_id Event ID
 * @param int $ticket_qty Number of tickets requested
 * @param string $pass_type Pass type (gold or violet)
 * @return array Availability status and message
 */
function check_ticket_availability_realtime($event_id, $ticket_qty, $pass_type)
{
	// Clear cache to get fresh data
	wp_cache_delete($event_id, 'post_meta');

	// Get current counts
	$sold_tickets = absint(get_post_meta($event_id, '_event_sold_tickets', true));
	$total_tickets = absint(get_post_meta($event_id, '_event_total_tickets', true));
	$available_tickets = $total_tickets - $sold_tickets;

	// Check total availability
	if ($ticket_qty > $available_tickets) {
		return array(
			'available' => false,
			'message' => "Only {$available_tickets} tickets available, but {$ticket_qty} requested."
		);
	}

	// Check pass type specific availability
	if ($pass_type === 'gold') {
		$sold_gold = absint(get_post_meta($event_id, '_event_sold_gold_tickets', true));
		$total_gold = absint(get_post_meta($event_id, '_event_total_gold_tickets', true));
		$available_gold = $total_gold - $sold_gold;

		if ($ticket_qty > $available_gold) {
			return array(
				'available' => false,
				'message' => "Only {$available_gold} Gold tickets available, but {$ticket_qty} requested."
			);
		}
	} elseif ($pass_type === 'violet') {
		$sold_violet = absint(get_post_meta($event_id, '_event_sold_violet_tickets', true));
		$total_violet = absint(get_post_meta($event_id, '_event_total_violet_tickets', true));
		$available_violet = $total_violet - $sold_violet;

		if ($ticket_qty > $available_violet) {
			return array(
				'available' => false,
				'message' => "Only {$available_violet} Violet tickets available, but {$ticket_qty} requested."
			);
		}
	}

	return array(
		'available' => true,
		'message' => 'Tickets are available.'
	);
}



// Modify post title format for order details
function modify_order_details_title($data)
{
	if ($data['post_type'] !== 'order_details') {
		return $data;
	}

	$order_id = isset($data['ID']) ? $data['ID'] : (isset($_POST['order_id']) ? absint($_POST['order_id']) : 0);
	$event_id = isset($_POST['event_id']) ? absint($_POST['event_id']) : 0;
	$customer_name = isset($_POST['customer_name']) ? sanitize_text_field($_POST['customer_name']) : '';

	if ($order_id && $event_id) {
		$event = get_post($event_id);
		if ($event) {
			$original_title = $event->post_title;
			$new_title = sprintf(
				'%s - %s - %d',
				$customer_name ?: 'Unknown Customer',
				$original_title,
				$event_id
			);
			$data['post_title'] = $new_title;
		}
	}

	return $data;
}

// Add custom columns to order details admin page
function order_details_custom_columns($columns)
{
	$new_columns = array(
		'cb' => $columns['cb'],
		'title' => 'Event Name',
		'date' => $columns['date'],
		'customer_name' => 'Customer Name',
		'customer_email' => 'Email',
		'customer_phone' => 'Phone',
		'payment_status' => 'Payment Status',
		'ticket_qty' => 'Ticket Quantity',
		'ticket_number' => 'Ticket Number'
	);
	return $new_columns;
}
add_filter('manage_order_details_posts_columns', 'order_details_custom_columns');

// Display custom column content
function order_details_custom_column_content($column, $post_id)
{
	switch ($column) {
		case 'title':
			$event_id = get_post_meta($post_id, '_order_event_id', true);
			if ($event_id) {
				$event = get_post($event_id);
				if ($event) {
					echo esc_html($event->post_title);
				} else {
					echo 'Event not found';
				}
			}
			break;
		case 'customer_name':
			echo esc_html(get_post_meta($post_id, '_order_customer_name', true));
			break;
		case 'customer_email':
			echo esc_html(get_post_meta($post_id, '_order_customer_email', true));
			break;
		case 'customer_phone':
			echo esc_html(get_post_meta($post_id, '_order_customer_phone', true));
			break;
		case 'payment_status':
			$status = get_post_meta($post_id, '_order_payment_status', true);
			$status_class = ($status === 'completed') ? 'completed' : 'pending';
			echo '<span class="order-status ' . esc_attr($status_class) . '">' . esc_html($status) . '</span>';
			break;
		case 'ticket_qty':
			echo esc_html(get_post_meta($post_id, '_order_ticket_qty', true));
			break;
		case 'ticket_number':
			$ticket_numbers = get_post_meta($post_id, '_order_ticket_numbers', true);
			if (is_array($ticket_numbers) && !empty($ticket_numbers)) {
				echo esc_html(implode(', ', $ticket_numbers));
			} else {
				echo 'No ticket assigned';
			}
			break;
	}
}
add_action('manage_order_details_posts_custom_column', 'order_details_custom_column_content', 10, 2);

// Add CSS for order status styling
function order_details_admin_styles()
{
	echo '<style>
		.order-status {
			display: inline-block;
			padding: 3px 8px;
			border-radius: 3px;
			font-weight: bold;
		}
		.order-status.completed {
			background-color: #c6e1c6;
			color: #5b841b;
		}
		.order-status.pending {
			background-color: #f8dda7;
			color: #94660c;
		}
	</style>';
}
add_action('admin_head', 'order_details_admin_styles');
add_filter('wp_insert_post_data', 'modify_order_details_title', 10, 1);


/************ Enqueue PDF Generator Script ************/
function ctf_enqueue_pdf_generator_script()
{
	if (is_admin() && isset($_GET['post_type']) && $_GET['post_type'] === 'order_details') {
		wp_enqueue_script('ctf-order-pdf', get_template_directory_uri() . '/js/order-pdf.js', array('jquery'), '1.0', true);
		wp_localize_script('ctf-order-pdf', 'ctf_order_pdf', array(
			'ajax_url' => admin_url('admin-ajax.php'),
			'nonce' => wp_create_nonce('ctf_generate_pdf_nonce')
		));
	}
}
add_action('admin_enqueue_scripts', 'ctf_enqueue_pdf_generator_script');

/************ Automatic Ticket Count Reconciliation System ************/

/**
 * Reconcile ticket counts by comparing with actual completed orders
 * This runs automatically to detect and fix discrepancies
 */
function reconcile_ticket_counts($event_id)
{
	global $wpdb;

	error_log("Starting automatic ticket count reconciliation for event {$event_id}");

	// Get all completed orders for this event
	$orders = get_posts(array(
		'post_type' => 'order_details',
		'post_status' => 'publish',
		'numberposts' => -1,
		'meta_query' => array(
			array(
				'key' => '_order_event_id',
				'value' => $event_id,
				'compare' => '='
			),
			array(
				'key' => '_order_payment_status',
				'value' => 'completed',
				'compare' => '='
			)
		)
	));

	$calculated_total = 0;
	$calculated_gold = 0;
	$calculated_violet = 0;

	foreach ($orders as $order) {
		$ticket_qty = absint(get_post_meta($order->ID, '_order_ticket_qty', true));
		$pass_type = get_post_meta($order->ID, '_order_pass_type', true);

		$calculated_total += $ticket_qty;

		if ($pass_type === 'gold') {
			$calculated_gold += $ticket_qty;
		} elseif ($pass_type === 'violet') {
			$calculated_violet += $ticket_qty;
		}
	}

	// Get current stored counts
	$stored_total = absint(get_post_meta($event_id, '_event_sold_tickets', true));
	$stored_gold = absint(get_post_meta($event_id, '_event_sold_gold_tickets', true));
	$stored_violet = absint(get_post_meta($event_id, '_event_sold_violet_tickets', true));

	// Check for discrepancies
	$total_diff = $calculated_total - $stored_total;
	$gold_diff = $calculated_gold - $stored_gold;
	$violet_diff = $calculated_violet - $stored_violet;

	if ($total_diff != 0 || $gold_diff != 0 || $violet_diff != 0) {
		error_log("Ticket count discrepancy detected for event {$event_id}:");
		error_log("Total: calculated={$calculated_total}, stored={$stored_total}, diff={$total_diff}");
		error_log("Gold: calculated={$calculated_gold}, stored={$stored_gold}, diff={$gold_diff}");
		error_log("Violet: calculated={$calculated_violet}, stored={$stored_violet}, diff={$violet_diff}");

		// Auto-fix the discrepancy
		$total_tickets = absint(get_post_meta($event_id, '_event_total_tickets', true));
		$total_gold = absint(get_post_meta($event_id, '_event_total_gold_tickets', true));
		$total_violet = absint(get_post_meta($event_id, '_event_total_violet_tickets', true));

		// Update sold tickets
		update_post_meta($event_id, '_event_sold_tickets', $calculated_total);
		update_post_meta($event_id, '_event_sold_gold_tickets', $calculated_gold);
		update_post_meta($event_id, '_event_sold_violet_tickets', $calculated_violet);

		// Update available tickets
		update_post_meta($event_id, '_event_available_tickets', $total_tickets - $calculated_total);
		update_post_meta($event_id, '_event_available_gold_tickets', $total_gold - $calculated_gold);
		update_post_meta($event_id, '_event_available_violet_tickets', $total_violet - $calculated_violet);

		// Clear cache
		wp_cache_delete($event_id, 'post_meta');

		error_log("Ticket counts automatically reconciled for event {$event_id}");

		// Send notification to admin
		$admin_email = get_option('admin_email');
		$event_title = get_the_title($event_id);
		$subject = "Ticket Count Discrepancy Auto-Fixed - {$event_title}";
		$message = "A ticket count discrepancy was detected and automatically fixed for event: {$event_title} (ID: {$event_id})\n\n";
		$message .= "Discrepancies found:\n";
		$message .= "Total tickets: {$total_diff}\n";
		$message .= "Gold tickets: {$gold_diff}\n";
		$message .= "Violet tickets: {$violet_diff}\n\n";
		$message .= "The counts have been automatically corrected based on completed orders.";

		wp_mail($admin_email, $subject, $message);
	} else {
		error_log("Ticket counts are consistent for event {$event_id}");
	}
}
add_action('reconcile_ticket_counts', 'reconcile_ticket_counts');

/**
 * Schedule regular reconciliation checks for all events
 */
function schedule_ticket_reconciliation()
{
	if (!wp_next_scheduled('run_ticket_reconciliation')) {
		wp_schedule_event(time(), 'hourly', 'run_ticket_reconciliation');
	}
}
add_action('wp', 'schedule_ticket_reconciliation');

/**
 * Run reconciliation for all active events
 */
function run_ticket_reconciliation()
{
	$events = get_posts(array(
		'post_type' => 'events',
		'post_status' => 'publish',
		'numberposts' => -1
	));

	foreach ($events as $event) {
		reconcile_ticket_counts($event->ID);
	}
}
add_action('run_ticket_reconciliation', 'run_ticket_reconciliation');

/**
 * Add AJAX handler for real-time ticket availability check
 */
function check_ticket_availability_ajax()
{
	check_ajax_referer('event_booking_nonce', 'nonce');

	$event_id = absint($_POST['event_id']);
	$ticket_qty = absint($_POST['ticket_qty']);
	$pass_type = sanitize_text_field($_POST['pass_type']);

	$availability = check_ticket_availability_realtime($event_id, $ticket_qty, $pass_type);

	wp_send_json($availability);
}
add_action('wp_ajax_check_ticket_availability', 'check_ticket_availability_ajax');
add_action('wp_ajax_nopriv_check_ticket_availability', 'check_ticket_availability_ajax');

/************ Razorpay Webhook Handler ************/
/**
 * Handle Razorpay webhook events
 * This endpoint processes payment events from Razorpay webhooks
 * Works alongside existing payment verification as a parallel system
 */
function handle_razorpay_webhook()
{
	// Get the raw POST data
	$webhook_body = file_get_contents('php://input');
	$webhook_signature = isset($_SERVER['HTTP_X_RAZORPAY_SIGNATURE']) ? $_SERVER['HTTP_X_RAZORPAY_SIGNATURE'] : '';

	// Log webhook attempt
	error_log('Razorpay webhook received. Signature: ' . $webhook_signature);

	// Verify webhook signature
	if (!verify_razorpay_webhook_signature($webhook_body, $webhook_signature)) {
		error_log('Razorpay webhook signature verification failed');
		http_response_code(400);
		exit('Invalid signature');
	}

	// Parse webhook payload
	$webhook_data = json_decode($webhook_body, true);
	if (!$webhook_data) {
		error_log('Razorpay webhook: Invalid JSON payload');
		http_response_code(400);
		exit('Invalid JSON');
	}

	// Log the event
	error_log('Razorpay webhook event: ' . $webhook_data['event'] . ' for entity: ' . $webhook_data['payload']['payment']['entity']['id']);

	// Process the webhook event
	$result = process_razorpay_webhook_event($webhook_data);

	if ($result) {
		http_response_code(200);
		exit('OK');
	} else {
		http_response_code(500);
		exit('Processing failed');
	}
}

/**
 * Verify Razorpay webhook signature
 * Uses the Razorpay SDK's built-in signature verification
 */
function verify_razorpay_webhook_signature($webhook_body, $webhook_signature)
{
	$webhook_secret = defined('RAZORPAY_WEBHOOK_SECRET') ? RAZORPAY_WEBHOOK_SECRET : '';

	if (!$webhook_secret || $webhook_secret === 'your_webhook_secret_here') {
		error_log('Razorpay webhook secret not configured');
		return false;
	}

	if (!$webhook_signature) {
		error_log('Razorpay webhook signature header missing');
		return false;
	}

	try {
		$api_key = defined('RAZORPAY_KEY') ? RAZORPAY_KEY : '';
		$api_secret = defined('RAZORPAY_SECRET') ? RAZORPAY_SECRET : '';

		if (!$api_key || !$api_secret) {
			error_log('Razorpay API credentials not configured');
			return false;
		}

		$api = new Razorpay\Api\Api($api_key, $api_secret);
		$api->utility->verifyWebhookSignature($webhook_body, $webhook_signature, $webhook_secret);

		return true;
	} catch (Exception $e) {
		error_log('Razorpay webhook signature verification error: ' . $e->getMessage());
		return false;
	}
}

/**
 * Process Razorpay webhook events
 * Currently handles payment.captured events
 */
function process_razorpay_webhook_event($webhook_data)
{
	$event = $webhook_data['event'];

	switch ($event) {
		case 'payment.captured':
			return process_payment_captured_webhook($webhook_data['payload']['payment']['entity']);

		case 'payment.failed':
			return process_payment_failed_webhook($webhook_data['payload']['payment']['entity']);

		default:
			error_log('Razorpay webhook: Unhandled event type: ' . $event);
			return true; // Return true for unhandled events to avoid retries
	}
}

/**
 * Process payment.captured webhook event
 * This handles successful payments received via webhook
 */
function process_payment_captured_webhook($payment_data)
{
	$payment_id = $payment_data['id'];
	$order_id_from_receipt = null;

	// Extract order ID from receipt (format: order_123)
	if (isset($payment_data['order_id'])) {
		$razorpay_order_id = $payment_data['order_id'];

		// Find the WordPress order by Razorpay order ID
		$orders = get_posts(array(
			'post_type' => 'order_details',
			'meta_query' => array(
				array(
					'key' => '_razorpay_order_id',
					'value' => $razorpay_order_id,
					'compare' => '='
				)
			),
			'posts_per_page' => 1,
			'post_status' => 'publish'
		));

		if (empty($orders)) {
			error_log('Razorpay webhook: Order not found for Razorpay order ID: ' . $razorpay_order_id);
			return false;
		}

		$order_id_from_receipt = $orders[0]->ID;
	}

	if (!$order_id_from_receipt) {
		error_log('Razorpay webhook: Could not determine order ID from payment data');
		return false;
	}

	$order_id = $order_id_from_receipt;

	// Check if this payment has already been processed
	$existing_payment_id = get_post_meta($order_id, '_order_payment_id', true);
	if ($existing_payment_id === $payment_id) {
		error_log('Razorpay webhook: Payment already processed for order_id: ' . $order_id . ', payment_id: ' . $payment_id);
		return true; // Already processed, return success
	}

	// Check if order is already completed
	$payment_status = get_post_meta($order_id, '_order_payment_status', true);
	if ($payment_status === 'completed') {
		error_log('Razorpay webhook: Order already completed for order_id: ' . $order_id);
		return true; // Already completed, return success
	}

	// Verify payment amount matches order amount
	$order_total = floatval(get_post_meta($order_id, '_order_total_amount', true)) * 100; // Convert to paise
	$payment_amount = $payment_data['amount'];

	if ($payment_amount != $order_total) {
		error_log('Razorpay webhook: Payment amount mismatch for order_id: ' . $order_id . '. Expected: ' . $order_total . ', Got: ' . $payment_amount);
		return false;
	}

	// Update payment status and details
	$old_status = get_post_meta($order_id, '_order_payment_status', true);
	update_post_meta($order_id, '_order_payment_status', 'completed');
	update_post_meta($order_id, '_order_payment_id', $payment_id);
	update_post_meta($order_id, '_order_payment_method', $payment_data['method']);
	update_post_meta($order_id, '_order_payment_time', current_time('mysql'));
	update_post_meta($order_id, '_order_processed_via', 'webhook'); // Mark as processed via webhook

	// If status changed from non-completed to completed, schedule reconciliation
	if ($old_status !== 'completed') {
		$event_id = get_post_meta($order_id, '_order_event_id', true);
		wp_schedule_single_event(time() + 30, 'reconcile_ticket_counts', array($event_id));
	}

	// Generate ticket numbers and send emails
	$event_id = get_post_meta($order_id, '_order_event_id', true);
	$ticket_qty = get_post_meta($order_id, '_order_ticket_qty', true);

	// Check if tickets were already generated
	$existing_tickets = get_post_meta($order_id, '_order_ticket_numbers', true);
	if (empty($existing_tickets)) {
		$ticket_numbers = generate_unique_ticket_numbers($event_id, $ticket_qty, $order_id);
		send_booking_emails($order_id, $event_id, $ticket_numbers);

		error_log('Razorpay webhook: Order processed successfully via webhook for order_id: ' . $order_id . ', payment_id: ' . $payment_id);
	} else {
		error_log('Razorpay webhook: Tickets already generated for order_id: ' . $order_id . ', skipping email');
	}

	return true;
}

/**
 * Process payment.failed webhook event
 * This handles failed payments received via webhook
 */
function process_payment_failed_webhook($payment_data)
{
	$payment_id = $payment_data['id'];
	$error_description = isset($payment_data['error_description']) ? $payment_data['error_description'] : 'Payment failed';

	// Find the order by Razorpay order ID
	if (isset($payment_data['order_id'])) {
		$razorpay_order_id = $payment_data['order_id'];

		$orders = get_posts(array(
			'post_type' => 'order_details',
			'meta_query' => array(
				array(
					'key' => '_razorpay_order_id',
					'value' => $razorpay_order_id,
					'compare' => '='
				)
			),
			'posts_per_page' => 1,
			'post_status' => 'publish'
		));

		if (!empty($orders)) {
			$order_id = $orders[0]->ID;

			// Update payment status to failed
			update_post_meta($order_id, '_order_payment_status', 'failed');
			update_post_meta($order_id, '_order_payment_error', $error_description);
			update_post_meta($order_id, '_order_payment_id', $payment_id);

			error_log('Razorpay webhook: Payment failed for order_id: ' . $order_id . ', payment_id: ' . $payment_id . ', error: ' . $error_description);
		}
	}

	return true;
}

// Register the webhook endpoint
// This creates a public endpoint that Razorpay can call
add_action('wp_ajax_nopriv_razorpay_webhook', 'handle_razorpay_webhook');
add_action('wp_ajax_razorpay_webhook', 'handle_razorpay_webhook');

/**
 * Manual trigger for immediate reconciliation
 */
function manual_reconcile_all_events()
{
	if (!current_user_can('manage_options')) {
		wp_die('Access denied');
	}

	$events = get_posts(array(
		'post_type' => 'events',
		'post_status' => 'publish',
		'numberposts' => -1
	));

	$fixed_events = 0;
	foreach ($events as $event) {
		// Get counts before reconciliation
		$stored_total_before = absint(get_post_meta($event->ID, '_event_sold_tickets', true));

		reconcile_ticket_counts($event->ID);

		// Check if anything was fixed
		$stored_total_after = absint(get_post_meta($event->ID, '_event_sold_tickets', true));
		if ($stored_total_before !== $stored_total_after) {
			$fixed_events++;
		}
	}

	echo '<div class="notice notice-success"><p>Reconciliation completed for ' . count($events) . ' events. ' . $fixed_events . ' events had discrepancies that were fixed.</p></div>';
}

/**
 * Add admin menu for manual reconciliation
 */
function add_ticket_reconciliation_menu()
{
	add_submenu_page(
		'edit.php?post_type=events',
		'Ticket Count Reconciliation',
		'Fix Ticket Counts',
		'manage_options',
		'ticket-reconciliation',
		'ticket_reconciliation_page'
	);
}
add_action('admin_menu', 'add_ticket_reconciliation_menu');

/**
 * Admin page for ticket reconciliation
 */
function ticket_reconciliation_page()
{
	if (isset($_POST['reconcile_now']) && wp_verify_nonce($_POST['reconcile_nonce'], 'reconcile_tickets')) {
		manual_reconcile_all_events();
	}

	?>
		<div class="wrap">
			<h1>Ticket Count Reconciliation</h1>
			<p>This tool will check all events for ticket count discrepancies and automatically fix them.</p>

			<div class="card">
				<h2>Automatic Reconciliation</h2>
				<p>The system automatically checks for discrepancies every hour and after each ticket purchase.</p>
				<p><strong>Next scheduled check:</strong> <?php echo wp_next_scheduled('run_ticket_reconciliation') ? date('Y-m-d H:i:s', wp_next_scheduled('run_ticket_reconciliation')) : 'Not scheduled'; ?></p>
			</div>

			<div class="card">
				<h2>Manual Reconciliation</h2>
				<p>Click the button below to immediately check and fix all ticket count discrepancies.</p>
				<form method="post">
					<?php wp_nonce_field('reconcile_tickets', 'reconcile_nonce'); ?>
					<input type="submit" name="reconcile_now" class="button button-primary" value="Reconcile All Events Now" onclick="return confirm('Are you sure you want to reconcile ticket counts for all events?');">
				</form>
			</div>

			<div class="card">
				<h2>Diagnostic Tool</h2>
				<p>For detailed analysis of ticket count issues, use the diagnostic tool:</p>
				<a href="<?php echo get_template_directory_uri(); ?>/ticket-count-diagnostic.php" class="button" target="_blank">Open Diagnostic Tool</a>
			</div>
		</div>
	<?php
}

/********************************** Event Booking End *********************************/
