# Razorpay Webhook Integration Guide

## Overview

This guide explains how to safely deploy Razorpay webhooks to your live ticket booking system without any downtime. The webhook integration works alongside your existing payment verification system as a backup safety mechanism.

## What Was Implemented

### 1. Webhook Endpoint
- **URL**: `https://yourdomain.com/wp-admin/admin-ajax.php?action=razorpay_webhook`
- **Method**: POST
- **Security**: HMAC-SHA256 signature verification using Razorpay SDK

### 2. Dual Processing System
- **Primary**: Existing frontend payment verification (unchanged)
- **Secondary**: Webhook processing (new, parallel system)
- **Safety**: Duplicate prevention ensures no double-processing

### 3. Events Handled
- `payment.captured`: Processes successful payments
- `payment.failed`: Logs failed payments
- Other events are logged but ignored

## Deployment Steps

### Step 1: Configure Webhook Secret

1. Log into your Razorpay Dashboard
2. Go to Settings → Webhooks
3. Create a new webhook or edit existing one
4. Generate a webhook secret (or copy existing one)
5. Update `wp-config.php`:

```php
define('RAZORPAY_WEBHOOK_SECRET', 'your_actual_webhook_secret_here');
```

### Step 2: Configure Webhook URL in Razorpay Dashboard

1. In Razorpay Dashboard → Webhooks
2. Set webhook URL to: `https://yourdomain.com/wp-admin/admin-ajax.php?action=razorpay_webhook`
3. Select these events:
   - `payment.captured`
   - `payment.failed`
4. Set webhook to "Active"

### Step 3: Test the Integration

Run the test script to verify everything works:

```bash
cd wp-content/themes/cosmictreefoundation
php test-webhook.php
```

All tests should pass before going live.

### Step 4: Monitor the System

After deployment, monitor these logs:
- WordPress error logs for webhook processing
- Razorpay dashboard for webhook delivery status
- Order completion rates

## How It Works

### Normal Flow (No Issues)
1. User completes payment
2. Webhook processes payment immediately
3. User receives ticket email instantly
4. Frontend verification finds payment already processed

### Fallback Flow (Webhook Issues)
1. User completes payment
2. Webhook fails or is delayed
3. Frontend verification processes payment normally
4. User receives ticket email via frontend flow

### Safety Mechanisms

1. **Duplicate Prevention**: Both systems check if payment is already processed
2. **Signature Verification**: All webhooks are cryptographically verified
3. **Amount Validation**: Payment amounts are verified against order totals
4. **Existing Flow Preserved**: Original payment flow remains unchanged

## Troubleshooting

### Webhook Not Receiving Events
- Check webhook URL is correct in Razorpay dashboard
- Verify webhook is set to "Active"
- Check server firewall allows incoming connections

### Signature Verification Fails
- Verify `RAZORPAY_WEBHOOK_SECRET` matches Razorpay dashboard
- Check webhook secret hasn't been regenerated
- Ensure no extra spaces in the secret

### Duplicate Tickets Generated
- This should not happen due to built-in prevention
- Check logs for `_order_processed_via` meta field
- Verify both webhook and frontend aren't processing simultaneously

### Orders Not Completing
- Check WordPress error logs
- Verify Razorpay API credentials are correct
- Ensure database connections are stable

## Monitoring Commands

### Check Recent Webhook Activity
```bash
tail -f /path/to/wordpress/wp-content/debug.log | grep "Razorpay webhook"
```

### Check Order Processing Method
```sql
SELECT post_id, meta_value 
FROM wp_postmeta 
WHERE meta_key = '_order_processed_via' 
ORDER BY post_id DESC 
LIMIT 10;
```

### Verify No Duplicate Processing
```sql
SELECT p.ID, p.post_title, 
       pm1.meta_value as payment_status,
       pm2.meta_value as processed_via
FROM wp_posts p
LEFT JOIN wp_postmeta pm1 ON p.ID = pm1.post_id AND pm1.meta_key = '_order_payment_status'
LEFT JOIN wp_postmeta pm2 ON p.ID = pm2.post_id AND pm2.meta_key = '_order_processed_via'
WHERE p.post_type = 'order_details' 
AND pm1.meta_value = 'completed'
ORDER BY p.ID DESC 
LIMIT 20;
```

## Benefits

1. **Faster Ticket Delivery**: Users get tickets immediately after payment
2. **Improved Reliability**: Dual-system approach prevents lost payments
3. **Zero Downtime**: Existing system continues working during deployment
4. **Better User Experience**: Reduced waiting time for confirmations
5. **Automatic Recovery**: Missed webhooks are handled by existing flow

## Files Modified

- `wp-config.php`: Added webhook secret configuration
- `functions.php`: Added webhook handling functions
- `test-webhook.php`: Test script for verification

## Support

If you encounter issues:
1. Run the test script to verify configuration
2. Check WordPress error logs
3. Verify Razorpay dashboard webhook settings
4. Ensure all safety mechanisms are working

The system is designed to be fail-safe - if webhooks stop working, your existing payment system will continue processing orders normally.
