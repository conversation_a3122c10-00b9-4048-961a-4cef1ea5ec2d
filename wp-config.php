<?php

/**
 * The base configuration for WordPress
 *
 * The wp-config.php creation script uses this file during the installation.
 * You don't have to use the website, you can copy this file to "wp-config.php"
 * and fill in the values.
 *
 * This file contains the following configurations:
 *
 * * Database settings
 * * Secret keys
 * * Database table prefix
 * * ABSPATH
 *
 * @link https://developer.wordpress.org/advanced-administration/wordpress/wp-config/
 *
 * @package WordPress
 */

// ** Database settings - You can get this info from your web host ** //
/** The name of the database for WordPress */
define('DB_NAME', 'eventbooking');

/** Database username */
define('DB_USER', 'root');

/** Database password */
define('DB_PASSWORD', 'Wedo@123');

/** Database hostname */
define('DB_HOST', 'localhost');

/** Database charset to use in creating database tables. */
define('DB_CHARSET', 'utf8');

/** The database collate type. Don't change this if in doubt. */
define('DB_COLLATE', '');

/**#@+
 * Authentication unique keys and salts.
 *
 * Change these to different unique phrases! You can generate these using
 * the {@link https://api.wordpress.org/secret-key/1.1/salt/ WordPress.org secret-key service}.
 *
 * You can change these at any point in time to invalidate all existing cookies.
 * This will force all users to have to log in again.
 *
 * @since 2.6.0
 */
define('AUTH_KEY',         '=:cj!NJh7&M+=^^sxJZ[d!(8(UresI{CS+2)Qh.OT]3QA,Mlc+gAZMiWx~qPMP:q');
define('SECURE_AUTH_KEY',  'P+|:n>RqkwTVcJEp*qDD`,kdOf(Z(Y+ezBVe-{dPT1buta7t(5W,1}99ZHj@Yv-z');
define('LOGGED_IN_KEY',    'UF#3+`)&bg4:4u_R8XbFpn&q|x3x>S]^lcrkjiG{YIJ8554AV}%>oS8yF+a}5?UD');
define('NONCE_KEY',        '6ucR-gUYW-z@fE!5TE(HJf:TW,Md1(MT~;21dTA 1tYXz)-WWEptBU,j,-&+xJAN');
define('AUTH_SALT',        'UQ/boQly1Q`%c6+Pj*AzP`t.`!o]|<dvA:4+QS+d~99Fa.|Syy%U}2~XFE9nKxP8');
define('SECURE_AUTH_SALT', 'O&[<e?|[uuRf-<*|,HJoAl-Ed|! Pf^^T0&pN4}-r#A;Om3o~&NJr&SVw0j)9X2L');
define('LOGGED_IN_SALT',   'H2[<rn.yh: 6ZhVgz81aya`cK1MLmd-{F/>h3}DM]iLf:rCb]O+S<YeI-U+VnQ{i');
define('NONCE_SALT',       'S+}0%yF/H[m=I[XhJCw~=Kx-S0k.x~f+1D]+yL!={t^3_EQ*oz-|qQe ~%+,~sn;');

/**#@-*/

/**
 * WordPress database table prefix.
 *
 * You can have multiple installations in one database if you give each
 * a unique prefix. Only numbers, letters, and underscores please!
 *
 * At the installation time, database tables are created with the specified prefix.
 * Changing this value after WordPress is installed will make your site think
 * it has not been installed.
 *
 * @link https://developer.wordpress.org/advanced-administration/wordpress/wp-config/#table-prefix
 */
$table_prefix = 'wp_';

/**
 * For developers: WordPress debugging mode.
 *
 * Change this to true to enable the display of notices during development.
 * It is strongly recommended that plugin and theme developers use WP_DEBUG
 * in their development environments.
 *
 * For information on other constants that can be used for debugging,
 * visit the documentation.
 *
 * @link https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/
 */
define('WP_DEBUG', true);
define('WP_DEBUG_LOG', true);
define('WP_DEBUG_DISPLAY', false);
define('FS_METHOD', 'direct');
define('RAZORPAY_KEY', 'rzp_test_5VF8FMcr2iWy5P');
define('RAZORPAY_SECRET', 'oAfLM130zCcNkZKWzWsqdwIH');
define('RAZORPAY_WEBHOOK_SECRET', 'Wedo@123'); // Replace with actual webhook secret from Razorpay dashboard
define('WP_HOME', 'http://event-booking.local/');
define('WP_SITEURL', 'http://event-booking.local/');

// Event Booking Constants
define('EARLY_BIRD_CUTOFF_DATE', '2025-07-15');
define('COUPON_CODE', 'EL1000');
define('COUPON_DISCOUNT', 1000);
define('COUPON_CODE_FULL_DISCOUNT', 'MGH100DC');
/* Add any custom values between this line and the "stop editing" line. */



/* That's all, stop editing! Happy publishing. */

/** Absolute path to the WordPress directory. */
if (! defined('ABSPATH')) {
  define('ABSPATH', __DIR__ . '/');
}

/** Sets up WordPress vars and included files. */
require_once ABSPATH . 'wp-settings.php';
